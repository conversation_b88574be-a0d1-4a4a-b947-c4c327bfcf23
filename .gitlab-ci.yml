build_image:
    stage: build
    only:
        - main
        - develop
    before_script:
        - echo "$ALIYUN_DOCKER_PASSWORD" | docker login $ALIYUN_DOCKER_REGISTRY --username "$ALIYUN_DOCKER_USERNAME" --password-stdin
        - "[[ $CI_COMMIT_BRANCH == main ]] && NAMESPACE=optimism-prod || NAMESPACE=optimism-test"
        - "[[ $CI_COMMIT_BRANCH == main ]] && ENV=prod || ENV=test"
    script: |
        docker build --rm \
          --build-arg GITLAB_TOKEN_NAME=$GITLAB_TOKEN_NAME \
          --build-arg GITLAB_TOKEN=$GITLAB_TOKEN \
          --build-arg ENV=$ENV \
          --add-host gitlab.tslsmart.com:************ \
          -t $ALIYUN_DOCKER_REGISTRY/$NAMESPACE/wishear-web:$CI_COMMIT_SHORT_SHA \
          -t $ALIYUN_DOCKER_REGISTRY/$NAMESPACE/wishear-web:latest .
        docker push $ALIYUN_DOCKER_REGISTRY/$NAMESPACE/wishear-web:$CI_COMMIT_SHORT_SHA
        docker push $ALIYUN_DOCKER_REGISTRY/$NAMESPACE/wishear-web:latest
