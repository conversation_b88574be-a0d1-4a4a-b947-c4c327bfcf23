FROM node:18.19.0-alpine3.17 as build

ARG ENV
ARG GITLAB_TOKEN_NAME
ARG GITLAB_TOKEN

RUN apk add --no-cache git

WORKDIR /app

# Install dependencies
COPY package.json package.json
COPY package-lock.json package-lock.json
COPY .npmrc .npmrc

RUN git config --global url."http://${GITLAB_TOKEN_NAME}:${GITLAB_TOKEN}@gitlab.tslsmart.com:7964".insteadOf "http://gitlab.tslsmart.com:7964" && \
  npm ci --unsafe-perm=true

# Build
COPY public public
COPY .env .env
COPY .env.development .env.development
COPY .npmrc .npmrc
COPY tsconfig.json tsconfig.json
COPY tsconfig.app.json tsconfig.app.json
COPY tsconfig.node.json tsconfig.node.json
COPY index.html index.html
COPY postcss.config.js postcss.config.js
COPY tailwind.config.ts tailwind.config.ts
COPY components.json components.json
COPY vite.config.ts vite.config.ts
COPY vite-env.d.ts vite-env.d.ts
COPY eslint.config.js eslint.config.js
COPY src src

RUN ENV=${ENV} npm run build

# Deploy
FROM nginx:stable-alpine

COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
