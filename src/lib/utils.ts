import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge";
import * as THREE from "three";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
export function hexToRGBA(hex: string, opacity: number): string {
  if (!hex || opacity === undefined) return null;

  // 移除可能存在的 # 前缀
  hex = hex.replace('#', '');

  // 将 3 位色值转换为 6 位
  if (hex.length === 3) {
    hex = hex.split('').map(char => char + char).join('');
  }

  // 转换为 RGB 值
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  // 返回 rgba 格式
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
}

//随机生成字符串
export function generateRandomState() {
  return Math.random().toString(36).substring(2, 15); // 生成随机字符串
}

// 将小数点换为 汉字点
export function convertNumberFormat(text) {
  // 处理小数点的情况
  let result = text.replace(/\d+\.\d+/g, (match) => {
    const [integer, decimal] = match.split(".");
    // 将小数部分的每个数字用空格分隔
    const spacedDecimal = decimal.split("").join(" ");
    return `${integer}点${spacedDecimal}`;
  });

  // 处理百分比的情况
  result = result.replace(
    /\d+%/g,
    (match) => "百分之" + match.replace("%", "")
  );

  return result;
}

export function getRippleColor(backgroundColor: string, opacity: number): string {
  // 如果是 hex 颜色，转换为 rgba
  if (backgroundColor.startsWith('#')) {
    return hexToRGBA(backgroundColor, opacity);
  }

  // 如果已经是 rgba 或 rgb，提取颜色值并设置新的透明度
  const rgbMatch = backgroundColor.match(/\d+/g);
  if (rgbMatch) {
    const [r, g, b] = rgbMatch;
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }

  return backgroundColor;
}

export const base64ToFile = (base64Str, filename = 'image') => {
  const [header, data] = base64Str.split(',');
  const mimeType = header.match(/:(.*?);/)[1];
  const bytes = Uint8Array.from(atob(data), c => c.charCodeAt(0));
  return new File([bytes], `${filename}.${mimeType.split('/')[1]}`, { type: mimeType });
};

export const hexToVec3 = (hex) => {
  const r = parseInt(hex.slice(1, 3), 16) / 255;
  const g = parseInt(hex.slice(3, 5), 16) / 255;
  const b = parseInt(hex.slice(5, 7), 16) / 255;
  return new THREE.Vector3(r, g, b);
};

export function createChromaKeyMaterial(texture, keyColorVec3, threshold, smoothing) {
  return new THREE.ShaderMaterial({
    uniforms: {
      videoTexture: { value: texture },
      keyColor: { value: keyColorVec3 },
      threshold: { value: threshold },
      smoothing: { value: smoothing },
    },
    vertexShader: `
      varying vec2 vUv;
      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `,
    fragmentShader: `
      uniform sampler2D videoTexture;
      uniform vec3 keyColor;
      uniform float threshold;
      uniform float smoothing;
      varying vec2 vUv;
      
      void main() {
        vec4 color = texture2D(videoTexture, vUv);
        float distance = length(color.rgb - keyColor);
        float alpha = smoothstep(threshold, threshold + smoothing, distance);
        gl_FragColor = vec4(color.rgb, alpha);
      }
    `,
    transparent: true,
  });
}