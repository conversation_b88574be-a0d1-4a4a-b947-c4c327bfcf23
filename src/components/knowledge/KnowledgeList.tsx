import { useState, useEffect } from "react";
import {
  PlusCircle,
  Search,
  FilterX,
  AlertCircle,
  FileText,
  FileQuestion,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { motion } from "framer-motion";
import { staggerContainer, staggerItem } from "@/lib/animations";
import { useToast } from "@/components/ui/use-toast";
import axios from "axios";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import KnowledgeCard from "@/components/ui/KnowledgeCard";
import CreateKnowledgeBase from "./CreateKnowledgeBase";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import DocumentManager from "./DocumentManager";
import {
  AKnowledge,
  type Knowledge,
  Knowledges,
  type KnowledgeList,
  UKnowledge,
  DKnowledge,
} from "@/request/knowledge";
import type { Response } from "../vite-env";
import { useRequest } from "ahooks";

const KnowledgeList = () => {
  const [knowledgeBases, setKnowledgeBases] = useState<Knowledge[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  const [editingKnowledgeBase, setEditingKnowledgeBase] =
    useState<Partial<Knowledge> | null>(null);
  const [isDocumentManagerOpen, setIsDocumentManagerOpen] = useState(false);
  const [currentKnowledgeBase, setCurrentKnowledgeBase] =
    useState<Partial<Knowledge> | null>(null);

  const { toast } = useToast();
  const [pageSize] = useState(10);
  const [pageNumber, setPageNumber] = useState(1);
  const [more, setMore] = useState(false);
  useEffect(() => {
    if (pageNumber > 1) {
      runAll({ pageSize, pageNumber, name: searchTerm || undefined });
    }
  }, [pageNumber]);

  const { run: runAdd } = useRequest(AKnowledge, {
    manual: true,
    onSuccess: () => {
      toast({
        title: "知识库已创建",
        description: `知识库 ${name} 创建成功`,
      });
      setPageNumber(1);
      setKnowledgeBases([]);
      runAll({ pageNumber: 1, pageSize, name: searchTerm || undefined });
    },
    onError: (e) => {
      toast({
        title: "知识库创建失败",
        description: `${e.message ?? "请检查网络或联系系统管理员"}`,
        variant: "destructive",
      });
    },
    onFinally: () => {
      setIsCreating(false);
    },
  });

  const { run: runUpdate } = useRequest(UKnowledge, {
    manual: true,
    onSuccess: (_, params) => {
      toast({
        title: "知识库已修改",
        description: `知识库修改成功`,
      });
      setKnowledgeBases((preItems: Knowledge[]) => {
        return preItems.map((item) => {
          if (item.id === editingKnowledgeBase.id) {
            (item.name = params[0].name),
              (item.description = params[0].description);
          }
          return item;
        });
      });
    },
    onError: (e) => {
      toast({
        title: "知识库修改失败",
        description: `${e.message ?? "请检查网络或联系系统管理员"}`,
        variant: "destructive",
      });
    },
    onFinally: () => {
      setIsCreating(false);
      setEditingKnowledgeBase(null);
    },
  });

  const { run: runAll, loading: isLoading } = useRequest(Knowledges, {
    defaultParams: [
      {
        pageNumber,
        pageSize,
        name: searchTerm || undefined,
      },
    ],
    onSuccess: (data: Response<KnowledgeList>) => {
      if (knowledgeBases.length + data.data.items.length >= data.data.total) {
        setMore(false);
      } else {
        setMore(true);
      }
      if (data.data.items) {
        setKnowledgeBases([...knowledgeBases, ...data.data.items]);
      }
    },
  });

  const { run: runDelete } = useRequest(DKnowledge, {
    manual: true,

    onSuccess: () => {
      toast({
        title: "知识库已删除",
        description: `知识库删除成功`,
      });
      setPageNumber(1);
      setKnowledgeBases([]);
      runAll({ pageNumber: 1, pageSize, name: searchTerm || undefined });
    },
    onError: (e) => {
      let message;
      if (e?.response?.data?.code == "10009") {
        message = "知识库使用中，请先在项目列表中删除关联的项目";
      }
      toast({
        title: "知识库删除失败",
        description: `${message || e.message || "请联系系统管理员"}`,
        variant: "destructive",
      });
    },
  });

  const handleCreateKnowledgeBase = async (
    newKnowledgeBase: Partial<Knowledge>
  ) => {
    if (editingKnowledgeBase) {
      runUpdate({ id: editingKnowledgeBase.id, ...newKnowledgeBase });
    } else {
      runAdd(newKnowledgeBase);
    }
  };

  const handleEditKnowledgeBase = (knowledgeBase: Partial<Knowledge>) => {
    setEditingKnowledgeBase({
      id: knowledgeBase.id,
      name: knowledgeBase.name,
      description: knowledgeBase.description,
    });
    setIsCreating(true);
  };

  const handleDeleteKnowledgeBase = async (knowledgeBaseId: string) => {
    runDelete(knowledgeBaseId);
  };

  const handleSearch = (keyword: string) => {
    setSearchTerm(keyword.trim());
    setPageNumber(1);
    setKnowledgeBases([]);
    if (keyword) {
      runAll({ pageNumber: 1, pageSize, name: keyword.trim() });
    } else {
      runAll({ pageNumber: 1, pageSize });
    }
  };

  const handleClearSearch = () => {
    setSearchTerm("");
    setPageNumber(1);
    runAll({ pageNumber: 1, pageSize, name: undefined });
  };
  const handleChangeKnowledgeBase = (data: Partial<Knowledge>) => {
    setCurrentKnowledgeBase({ id: data.id, name: data.name });
    setIsDocumentManagerOpen(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <h1 className="text-2xl font-semibold">我的知识库</h1>
        <Button
          onClick={() => {
            setEditingKnowledgeBase(null);
            setIsCreating(true);
          }}
          className="gap-1"
        >
          <PlusCircle className="h-4 w-4" />
          创建新知识库
        </Button>
      </div>

      <div className="relative">
        <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="搜索知识库..."
          value={searchTerm}
          onChange={(e) => handleSearch(e.target.value)}
          className="pl-9 pr-9"
        />
        {searchTerm && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-1 top-1 h-8 w-8 text-muted-foreground"
            onClick={handleClearSearch}
          >
            <FilterX className="h-4 w-4" />
          </Button>
        )}
      </div>

      {isLoading ? (
        <div className="flex justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : knowledgeBases.length > 0 ? (
        <>
          <motion.div
            variants={staggerContainer}
            initial="initial"
            animate="animate"
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {knowledgeBases.map((knowledgeBase) => (
              <motion.div key={knowledgeBase.id} variants={staggerItem}>
                <KnowledgeCard
                  {...knowledgeBase}
                  onEdit={() => handleEditKnowledgeBase(knowledgeBase)}
                  onDelete={handleDeleteKnowledgeBase}
                  onChange={handleChangeKnowledgeBase}
                />
              </motion.div>
            ))}
          </motion.div>
          {more && (
            <div
              style={{ cursor: "pointer" }}
              onClick={() => setPageNumber(pageNumber + 1)}
            >
              加载更多
            </div>
          )}
        </>
      ) : searchTerm ? (
        <Alert className="bg-muted/50 border-dashed">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            没有找到与 "{searchTerm}" 相关的知识库
          </AlertDescription>
        </Alert>
      ) : (
        <Alert className="bg-muted/50 border-dashed">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            没有文档知识库，点击"创建新知识库"开始创建
          </AlertDescription>
        </Alert>
      )}

      <Dialog open={isCreating} onOpenChange={setIsCreating}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {editingKnowledgeBase ? "编辑知识库" : "创建新知识库"}
            </DialogTitle>
            <DialogDescription>
              {editingKnowledgeBase
                ? "修改知识库信息和内容"
                : "创建一个新的知识库"}
            </DialogDescription>
          </DialogHeader>
          <CreateKnowledgeBase
            onSubmit={handleCreateKnowledgeBase}
            editingKnowledgeBase={editingKnowledgeBase}
            knowledgeBases={knowledgeBases}
          />
        </DialogContent>
      </Dialog>

      <Dialog
        open={isDocumentManagerOpen}
        onOpenChange={setIsDocumentManagerOpen}
      >
        <DialogContent className="sm:max-w-[900px] w-full max-h-[80vh] overflow-y-auto">
          {currentKnowledgeBase && (
            <DocumentManager
              knowledgeBaseId={currentKnowledgeBase.id}
              knowledgeBaseTitle={currentKnowledgeBase.name}
              onClose={() => setIsDocumentManagerOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default KnowledgeList;
