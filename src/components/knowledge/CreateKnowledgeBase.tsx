import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { DialogFooter } from "@/components/ui/dialog";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { FileText, FileQuestion, Upload, Plus, Trash } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { Knowledge } from "@/request/knowledge";

interface CreateKnowledgeBaseProps {
  onSubmit: (knowledgeBase: any) => void;
  editingKnowledgeBase: Partial<Knowledge> | null;
  knowledgeBases: any[];
}

const CreateKnowledgeBase = ({
  onSubmit,
  editingKnowledgeBase,
}: CreateKnowledgeBaseProps) => {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");

  // Find the knowledge base if we're editing
  useEffect(() => {
    if (editingKnowledgeBase) {
      setName(editingKnowledgeBase.name);
      setDescription(editingKnowledgeBase.description);
    }
  }, [editingKnowledgeBase]);

  // const handleDocumentUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const files = e.target.files;
  //   if (!files || files.length === 0) return;

  //   // In a real app, we would upload the file and get a URL
  //   // Here we just use the file name
  //   const newDocuments = Array.from(files).map((file) => file.name);
  //   setDocuments([...documents, ...newDocuments]);

  //   toast({
  //     title: "文档上传成功",
  //     description: `${newDocuments.length}个文档已成功上传`,
  //   });

  //   // Reset file input
  //   e.target.value = "";
  // };

  const handleSubmit = () => {
    console.log("======params", name, description);
    onSubmit({ name, description });
  };

  return (
    <div className="space-y-6 py-2">
      <div className="space-y-4">
        <div>
          <Label htmlFor="kb-title">知识库名称</Label>
          <Input
            id="kb-title"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="请输入知识库名称"
            className="mt-1.5"
          />
        </div>

        <div>
          <Label htmlFor="kb-description">知识库描述</Label>
          <Textarea
            id="kb-description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="请输入知识库描述"
            className="mt-1.5"
            rows={3}
          />
        </div>
      </div>

      <DialogFooter>
        <Button
          type="button"
          onClick={handleSubmit}
          disabled={!name || !description}
        >
          {editingKnowledgeBase ? "保存更改" : "创建知识库"}
        </Button>
      </DialogFooter>
    </div>
  );
};

export default CreateKnowledgeBase;
