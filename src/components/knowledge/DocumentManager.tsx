import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  FileText,
  FolderPlus,
  Upload,
  Trash,
  Pencil,
  FolderOpen,
  ArrowLeft,
} from "lucide-react";
import { motion } from "framer-motion";
import {
  type Folder,
  folders as getFolders,
  AFolder,
  DFolder,
  UFolder,
  PFolder,
  FolderWithParent,
} from "@/request/folder";
import type { Response } from "../vite-env";
import dayjs from "dayjs";
import {
  AFile,
  files as getFiles,
  FileFormat,
  DFile,
  UFile,
} from "@/request/files";
import { useRequest } from "ahooks";
import LoadingDialog from "@/components/ui/LoadingDialog";

interface DocumentManagerProps {
  knowledgeBaseId: string;
  knowledgeBaseTitle: string;
  onClose: () => void;
}

const DocumentManager = ({
  knowledgeBaseId,
  knowledgeBaseTitle,
  onClose,
}: DocumentManagerProps) => {
  // Demo data for documents and folders
  const [documents, setDocuments] = useState<FileFormat[]>([]);

  const [folders, setFolders] = useState<Folder[]>([]);

  const [currentFolder, setCurrentFolder] = useState<string | null>(null);
  const [parent, setParent] = useState<Partial<Folder> | null>(null);
  const [currentName, setCurrentName] = useState<string | null>(null);
  const [isNewFolderDialogOpen, setIsNewFolderDialogOpen] = useState(false);
  const [newFolderName, setNewFolderName] = useState("");
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false);
  const [renamingItem, setRenamingItem] = useState<{
    id: string;
    name: string;
    type: "folder" | "document";
  } | null>(null);
  const [newName, setNewName] = useState("");

  const { toast } = useToast();
  const [uploading, setUploading] = useState(false);

  const { run: runFolders } = useRequest(getFolders, {
    manual: true,
    onSuccess: (data: Response<Folder[]>) => {
      setFolders(data.data);
    },
    onError: (e) => {
      toast({
        title: "获取文件夹失败",
        description: `${e.message ?? "请检查网络或联系系统管理员"}`,
        variant: "destructive",
      });
    },
  });
  const { run: runFiles } = useRequest(getFiles, {
    manual: true,
    onSuccess: (data: Response<FileFormat[]>) => {
      setDocuments(data.data);
    },
    onError: (e) => {
      toast({
        title: "获取文件失败",
        description: `${e.message ?? "请检查网络或联系系统管理员"}`,
        variant: "destructive",
      });
    },
  });
  const { run: runAddFolder } = useRequest(AFolder, {
    manual: true,
    onSuccess: (data: Response<Folder>) => {
      toast({
        title: "创建文件夹成功",
        description: "创建文件夹成功",
      });
      if (!currentFolder) {
        runFolders(knowledgeBaseId);
      } else {
        runFolders(knowledgeBaseId, currentFolder);
      }
    },
    onError: (e) => {
      toast({
        title: "创建文件夹失败",
        description: `${e.message ?? "请检查网络或联系系统管理员"}`,
        variant: "destructive",
      });
    },
    onFinally: () => {
      setNewFolderName("");
      setIsNewFolderDialogOpen(false);
    },
  });
  const { run: runAddFile } = useRequest(AFile, {
    manual: true,
    onSuccess: () => {
      toast({
        title: "上传成功",
        description: "上传文件成功",
      });
      if (!currentFolder) {
        runFiles(knowledgeBaseId);
      } else {
        runFiles(knowledgeBaseId, currentFolder);
      }
    },
    onError: (e) => {
      let message;
      if (e?.response?.data?.code == "10012") {
        message = "文档上传知识库失败，请联系管理员处理";
      }
      toast({
        title: "上传文件失败",
        description: `${
          message ?? e.message ?? "请检查网络或联系系统管理员"
        }`,
        variant: "destructive",
      });
    },
    onFinally: () => {
      setUploading(false);
    },
  });

  const { run: runDeleteFile } = useRequest(DFile, {
    manual: true,
    onSuccess: () => {
      toast({
        title: "文件已删除",
        description: "删除文件成功",
      });
      if (!currentFolder) {
        runFiles(knowledgeBaseId);
      } else {
        runFiles(knowledgeBaseId, currentFolder);
      }
    },
    onError: (e) => {
      toast({
        title: "删除文件失败",
        description: `${
          e?.response?.data.code ?? e.message ?? "请检查网络或联系系统管理员"
        }`,
        variant: "destructive",
      });
    },
  });
  const { run: runDeleteFolder } = useRequest(DFolder, {
    manual: true,
    onSuccess: () => {
      toast({
        title: "文件夹已删除",
        description: "删除文件夹成功",
      });
      if (!currentFolder) {
        runFolders(knowledgeBaseId);
      } else {
        runFolders(knowledgeBaseId, currentFolder);
      }
    },
    onError: (e) => {
      toast({
        title: "删除文件夹失败",
        description: `${
          e?.response?.data.code ?? e.message ?? "请检查网络或联系系统管理员"
        }`,
        variant: "destructive",
      });
    },
  });
  const { run: runUpdateFolder } = useRequest(UFolder, {
    manual: true,
    onSuccess: () => {
      toast({
        title: "文件夹已更新",
        description: "文件夹更新成功",
      });
      if (!currentFolder) {
        runFolders(knowledgeBaseId);
      } else {
        runFolders(knowledgeBaseId, currentFolder);
      }
    },
    onError: (e) => {
      toast({
        title: "更新文件夹失败",
        description: `${e.message ?? "请检查网络或联系系统管理员"}`,
        variant: "destructive",
      });
    },
    onFinally: () => {
      setIsRenameDialogOpen(false);
    },
  });

  const { run: runUpdateFile } = useRequest(UFile, {
    manual: true,
    onSuccess: () => {
      toast({
        title: "文件已更新",
        description: "文件更新成功",
      });
      if (!currentFolder) {
        runFiles(knowledgeBaseId);
      } else {
        runFiles(knowledgeBaseId, currentFolder);
      }
    },
    onError: (e) => {
      toast({
        title: "更新文件失败",
        description: `${e.message ?? "请检查网络或联系系统管理员"}`,
        variant: "destructive",
      });
    },
    onFinally: () => {
      setIsRenameDialogOpen(false);
    },
  });

  const { run: runParent } = useRequest(PFolder, {
    manual: true,
    onSuccess: (data: Response<FolderWithParent[]>, params) => {
      if (data.data.length > 0 && data.data[0].parent) {
        console.log("=====parent", data.data[0].parent);
        setParent(data.data[0].parent);
      } else {
        setParent(null);
      }
    },
    onError: (e) => {
      toast({
        title: "获取父级文件夹失败",
        description: `${e.message ?? "请检查网络或联系系统管理员"}`,
        variant: "destructive",
      });
    },
  });
  useEffect(() => {
    if (!currentFolder) {
      runFolders(knowledgeBaseId);
      runFiles(knowledgeBaseId);
    } else {
      console.log("=====", currentFolder);
      runParent(knowledgeBaseId, currentFolder);
      runFolders(knowledgeBaseId, currentFolder);
      runFiles(knowledgeBaseId, currentFolder);
    }
  }, [currentFolder]);

  // Handle document upload
  const handleDocumentUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    const formData = new FormData();
    formData.append("file", files[0]);
    formData.append("knowledgeId", knowledgeBaseId);
    setUploading(true);
    if (currentFolder) {
      formData.append("folderId", currentFolder);
      runAddFile(formData);
    } else {
      runAddFile(formData);
    }
  };

  // Handle document deletion
  const handleDeleteDocument = (documentId: string) => {
    runDeleteFile(documentId);
  };

  // Handle folder creation
  const handleCreateFolder = () => {
    if (!newFolderName.trim()) {
      toast({
        title: "创建失败",
        description: "文件夹名称不能为空",
        variant: "destructive",
      });
      return;
    }
    if (currentFolder) {
      runAddFolder({
        knowledgeId: knowledgeBaseId,
        name: newFolderName,
        parentId: currentFolder,
      });
    } else {
      runAddFolder({ knowledgeId: knowledgeBaseId, name: newFolderName });
    }
  };

  // Handle folder deletion
  const handleDeleteFolder = (folderId: string) => {
    runDeleteFolder(folderId);
  };

  // Handle opening a folder
  const handleOpenFolder = (folderId: string) => {
    setCurrentFolder(folderId);
  };

  // Handle navigating back to parent folder
  const handleNavigateBack = () => {
    if (parent && parent.parentId) {
      setCurrentFolder(parent.parentId);
    } else {
      setCurrentFolder(null);
    }
  };

  // Handle rename dialog open
  const handleRenameDialogOpen = (
    id: string,
    name: string,
    type: "folder" | "document"
  ) => {
    setRenamingItem({ id, name, type });
    setNewName(name);
    setIsRenameDialogOpen(true);
  };

  // Handle rename
  const handleRename = () => {
    if (!newName.trim() || !renamingItem) {
      toast({
        title: "重命名失败",
        description: "名称不能为空",
        variant: "destructive",
      });
      return;
    }

    if (renamingItem.type === "folder") {
      runUpdateFolder({ id: renamingItem.id, name: newName });
    } else {
      runUpdateFile({ id: renamingItem.id, name: newName });
    }

    setIsRenameDialogOpen(false);
    setRenamingItem(null);

    toast({
      title: "重命名成功",
      description: `${
        renamingItem.type === "folder" ? "文件夹" : "文档"
      }已重命名为"${newName}"`,
    });
  };

  return (
    <>
      <LoadingDialog open={uploading} message={`正在上传文件...`} />
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <h2 className="text-xl font-semibold">
              {knowledgeBaseTitle} - 文档管理
            </h2>
            {currentFolder && (
              <Button
                variant="ghost"
                size="sm"
                className="ml-2"
                onClick={handleNavigateBack}
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                返回上级
              </Button>
            )}
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              className="gap-1"
              onClick={() => setIsNewFolderDialogOpen(true)}
            >
              <FolderPlus className="h-4 w-4" />
              新建文件夹
            </Button>
            <div className="relative">
              <Input
                type="file"
                className="hidden"
                id="file-upload"
                onChange={handleDocumentUpload}
                multiple
                accept=".pdf,.doc,.docx,.txt"
              />
              <Label
                htmlFor="file-upload"
                className="cursor-pointer flex items-center gap-1 rounded-md px-3 h-9 text-sm border bg-background hover:bg-accent hover:text-accent-foreground"
              >
                <Upload className="h-4 w-4" />
                上传文档
              </Label>
            </div>
          </div>
        </div>

        {/* Current path display */}
        <div className="text-sm text-muted-foreground">
          当前位置: {currentFolder ? parent?.name || "未知文件夹" : "根目录"}
        </div>

        {/* Folders and documents list */}
        <div className="border rounded-md overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[400px]">名称</TableHead>
                <TableHead>大小</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>上传/创建时间</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {folders.length === 0 && documents.length === 0 && (
                <TableRow>
                  <TableCell colSpan={5} className="h-24 text-center">
                    没有文件或文件夹
                  </TableCell>
                </TableRow>
              )}

              {/* Folders */}
              {folders.map((folder) => (
                <TableRow key={folder.id}>
                  <TableCell className="font-medium">
                    <div
                      className="flex items-center gap-2 cursor-pointer"
                      onClick={() => handleOpenFolder(folder.id)}
                    >
                      <FolderOpen className="h-4 w-4 text-amber-500" />
                      {folder.name}
                    </div>
                  </TableCell>
                  <TableCell>-</TableCell>
                  <TableCell>文件夹</TableCell>
                  <TableCell>
                    {folder.createTime
                      ? dayjs(folder.createTime).format("YYYY-MM-DD HH:mm:ss")
                      : ""}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRenameDialogOpen(
                            folder.id,
                            folder.name,
                            "folder"
                          );
                        }}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-destructive"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteFolder(folder.id);
                        }}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}

              {/* Documents */}
              {documents.map((doc) => (
                <TableRow key={doc.id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-blue-500" />
                      {doc.name}
                    </div>
                  </TableCell>
                  <TableCell>{doc.size}</TableCell>
                  <TableCell>{doc.mimeType}</TableCell>
                  <TableCell>
                    {doc.createTime
                      ? dayjs(doc.createTime).format("YYYY-MM-DD HH:mm:ss")
                      : ""}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                        onClick={() =>
                          handleRenameDialogOpen(doc.id, doc.name, "document")
                        }
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-destructive"
                        onClick={() => handleDeleteDocument(doc.id)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Button to close the document manager */}
        <div className="flex justify-end">
          <Button onClick={onClose}>完成</Button>
        </div>

        {/* New Folder Dialog */}
        <Dialog
          open={isNewFolderDialogOpen}
          onOpenChange={setIsNewFolderDialogOpen}
        >
          <DialogContent className="sm:max-w-[450px]">
            <DialogHeader>
              <DialogTitle>新建文件夹</DialogTitle>
              <DialogDescription>
                创建一个新的文件夹来组织您的文档
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <Label htmlFor="folder-name">文件夹名称</Label>
              <Input
                id="folder-name"
                value={newFolderName}
                onChange={(e) => setNewFolderName(e.target.value)}
                placeholder="请输入文件夹名称"
                className="mt-1.5"
              />
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsNewFolderDialogOpen(false)}
              >
                取消
              </Button>
              <Button onClick={handleCreateFolder}>创建</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Rename Dialog */}
        <Dialog open={isRenameDialogOpen} onOpenChange={setIsRenameDialogOpen}>
          <DialogContent className="sm:max-w-[450px]">
            <DialogHeader>
              <DialogTitle>重命名</DialogTitle>
              <DialogDescription>
                为{renamingItem?.type === "folder" ? "文件夹" : "文档"}重新命名
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <Label htmlFor="new-name">新名称</Label>
              <Input
                id="new-name"
                value={newName}
                onChange={(e) => setNewName(e.target.value)}
                placeholder="请输入新名称"
                className="mt-1.5"
              />
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsRenameDialogOpen(false)}
              >
                取消
              </Button>
              <Button onClick={handleRename}>保存</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
};

export default DocumentManager;
