import { useState, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { Play, Pause, Check, Plus, Volume2, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";

export interface AudioCardProps {
  id?: string;
  name: string;
  url: string;
  category?: string;
  isSelected?: boolean;
  isChoice?: boolean;
  onSelect?: (id: string) => void;
  onDelete?: (id: string) => void;
}

const AudioCard = ({
  id,
  name,
  url,
  category,
  isChoice = false,
  isSelected = false,
  onSelect,
  onDelete,
}: AudioCardProps) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [progress, setProgress] = useState(0);
  const [volume, setVolume] = useState(0.8);
  const audioRef = useRef<HTMLAudioElement>(null);

  const handlePlayToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isPlaying) {
      audioRef.current?.pause();
    } else {
      audioRef.current?.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleSelect = () => {
    if (onSelect) {
      onSelect(id);
    }
  };

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      const currentProgress =
        (audioRef.current.currentTime / audioRef.current.duration) * 100;
      setProgress(currentProgress);
    }
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDelete) {
      onDelete(id);
    }
  };

  const handleVolumeChange = (value: number[]) => {
    const newVolume = value[0];
    setVolume(newVolume);
    if (audioRef.current) {
      audioRef.current.volume = newVolume;
    }
  };

  const handleEnded = () => {
    setIsPlaying(false);
    setProgress(0);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 10 }}
      whileHover={{ y: -3 }}
      transition={{ duration: 0.3 }}
    >
      <Card
        className={cn(
          "overflow-hidden transition-all duration-300 h-full flex flex-col",
          isSelected ? "ring-2 ring-primary ring-offset-2" : "hover:shadow-md",
          "border-opacity-40"
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={handleSelect}
      >
        <audio
          ref={audioRef}
          src={url}
          onTimeUpdate={handleTimeUpdate}
          onEnded={handleEnded}
        />

        <CardContent className="p-4 pt-5 flex-grow relative">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="font-medium truncate">{name}</h3>
              <p className="text-sm text-muted-foreground mt-1">
                {category === "System" ? "系统音频" : "自定义音频"}
              </p>
            </div>

            {category === "System" && (
              <Badge variant="outline" className="ml-2 bg-secondary/50">
                系统
              </Badge>
            )}
            {category !== "System" && isHovered && onDelete && (
              <Button
                variant="destructive"
                size="icon"
                className="h-7 w-7 absolute top-2 right-2"
                onClick={handleDelete}
              >
                <Trash2 className="h-3.5 w-3.5" />
              </Button>
            )}
          </div>

          <div className="relative h-1 w-full bg-secondary rounded-full overflow-hidden mb-4">
            <motion.div
              className="absolute top-0 left-0 h-full bg-primary/70 rounded-full"
              style={{ width: `${progress}%` }}
            />
          </div>

          <AnimatePresence>
            {isHovered && (
              <motion.div
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0 }}
                className="flex items-center gap-2"
              >
                <Volume2 className="h-4 w-4 text-muted-foreground" />
                <Slider
                  value={[volume]}
                  min={0}
                  max={1}
                  step={0.01}
                  onValueChange={handleVolumeChange}
                  className="w-full"
                />
              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>

        <CardFooter className="p-4 pt-0 flex justify-between">
          <Button
            variant="outline"
            size="icon"
            className="rounded-full h-9 w-9 text-primary"
            onClick={handlePlayToggle}
          >
            {isPlaying ? (
              <Pause className="h-4 w-4" />
            ) : (
              <Play className="h-4 w-4 ml-0.5" />
            )}
          </Button>
          {isChoice && (
            <Button
              variant={isSelected ? "default" : "outline"}
              size="sm"
              className="gap-1"
              onClick={handleSelect}
            >
              {isSelected ? (
                <>
                  <Check className="h-4 w-4" />
                  已选择
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4" />
                  选择
                </>
              )}
            </Button>
          )}
        </CardFooter>
      </Card>
    </motion.div>
  );
};

export default AudioCard;
