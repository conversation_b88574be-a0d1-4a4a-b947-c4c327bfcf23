import { useState, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { Play, Pause, Check, Plus, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export interface ResourceCardProps {
  id?: string;
  type: string;
  category: string;
  name: string;
  url: string;
  isSelected?: boolean;
  isDemo?: boolean;
  isChoice?: boolean;
  showPlay?: boolean;
  onSelect?: (id: string) => void;
  onPause?: (path: string) => void;
  onPreview?: (path: string) => void;
  onDelete?: (id: string) => void;
}

const ResourceCard = ({
  id,
  name,
  url,
  type,
  category,
  isSelected = false,
  isDemo = false,
  isChoice = false,
  showPlay = false,
  onSelect,
  onPause,
  onPreview,
  onDelete,
}: ResourceCardProps) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  const handlePlayToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (type === "Video") {
      if (isPlaying) {
        videoRef.current?.pause();
      } else {
        videoRef.current?.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleSelect = () => {
    if (onSelect) {
      onSelect(id);
    }
  };
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDelete) {
      onDelete(id);
    }
  };

  const handleItem = () => {
    if (type == "Video") {
      onPause(url);
    } else {
      onPreview(url);
    }
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 10 }}
        whileHover={{ y: -5 }}
        transition={{ duration: 0.3 }}
      >
        <Card
          className={cn(
            "overflow-hidden transition-all duration-300 h-full flex flex-col",
            isSelected
              ? "ring-2 ring-primary ring-offset-2"
              : "hover:shadow-md",
            "border-opacity-40"
          )}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          onClick={handleSelect}
        >
          <div className="aspect-video relative overflow-hidden bg-muted">
            {type === "Video" ? (
              <video
                ref={videoRef}
                src={url}
                className="w-full  object-cover"
                onPlay={() => setIsPlaying(true)}
                onPause={() => setIsPlaying(false)}
                onEnded={() => setIsPlaying(false)}
              />
            ) : (
              <img src={url} alt={name} className="w-full object-cover" />
            )}

            <AnimatePresence>
              {(isHovered || isSelected) && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="absolute inset-0 bg-black/30 flex items-center justify-center"
                >
                  {showPlay && type === "Video" && (
                    <Button
                      size="icon"
                      variant="secondary"
                      className="rounded-full h-10 w-10 text-primary bg-white hover:bg-white hover:text-accent-foreground"
                      onClick={handlePlayToggle}
                    >
                      {isPlaying ? (
                        <Pause className="h-5 w-5" />
                      ) : (
                        <Play className="h-5 w-5 ml-0.5" />
                      )}
                    </Button>
                  )}
                  {category != "System" && onDelete && (
                    <Button
                      size="icon"
                      variant="destructive"
                      className="rounded-full h-8 w-8 absolute top-2 right-2"
                      onClick={handleDelete}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </motion.div>
              )}
            </AnimatePresence>

            {category === "System" && (
              <Badge
                variant="secondary"
                className="absolute top-2 left-2 opacity-80"
              >
                系统
              </Badge>
            )}

            {isChoice && isSelected && (
              <div className="absolute top-2 left-2">
                <Badge className="bg-primary">
                  <Check className="mr-1 h-3 w-3" />
                  已选择
                </Badge>
              </div>
            )}
          </div>

          <CardContent className="p-4 flex-grow">
            <h3 className="font-medium truncate">{name}</h3>
            <p className="text-sm text-muted-foreground mt-1 capitalize">
              {type === "Video" ? "视频形象" : "图片形象"}
            </p>
          </CardContent>

          <CardFooter className="p-4 pt-0 flex justify-between">
            {isChoice && (
              <Button
                variant={isSelected ? "default" : "outline"}
                size="sm"
                className="w-full gap-1"
                onClick={handleSelect}
              >
                {isSelected ? (
                  <>
                    <Check className="h-4 w-4" />
                    已选择
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4" />
                    选择
                  </>
                )}
              </Button>
            )}

            {!isChoice && (
              <Button
                variant="outline"
                size="sm"
                className="w-full gap-1"
                onClick={handleItem}
              >
                {type == "Video" ? "播放视频" : "预览图片"}
              </Button>
            )}
          </CardFooter>
        </Card>
      </motion.div>
    </>
  );
};

export default ResourceCard;
