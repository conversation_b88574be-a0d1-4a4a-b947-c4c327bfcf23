import { Loader2 } from "lucide-react";
import { Dialog, DialogContent } from "@/components/ui/dialog";

interface LoadingDialogProps {
  open: boolean;
  message?: string;
}

const LoadingDialog = ({
  open,
  message = "正在上传文件...",
}: LoadingDialogProps) => {
  return (
    <Dialog open={open}>
      <DialogContent className="sm:max-w-[425px] text-center p-8">
        <div className="flex flex-col items-center justify-center gap-4">
          <Loader2 className="h-10 w-10 animate-spin text-primary" />
          <p className="text-lg font-medium">{message}</p>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default LoadingDialog;
