import { useState, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { cn } from "@/lib/utils";
import {
  User,
  LogOut,
  Image,
  Music,
  Folders,
  BookOpen,
  FileText,
  Server,
  Settings,
  Users,
  ChevronDown,
  Edit,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { ResetPwd, type User as UserFormat } from "@/request/user";
import type { Response } from "../vite-env";
import { useRequest } from "ahooks";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "../ui/use-toast";
import store from "store2";

export const Navbar = () => {
  const { user, logout, isAuthenticated } = useAuth();
  const { toast } = useToast();
  const location = useLocation();
  const [scrolled, setScrolled] = useState(false);
  const isAdmin = user?.role === "admin_user";
  const [isReset, setIsReset] = useState(false);
  const [newUser, setNewUser] = useState({
    oldPassword: "",
    password: "",
    confirmPassword: "",
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const navigate = useNavigate();

  const { run } = useRequest(ResetPwd, {
    manual: true,
    onSuccess: () => {
      toast({
        title: "密码修改成功",
        description: `请重新登录`,
      });
      logout();
    },
    onError: (e) => {
      toast({
        title: "密码修改失败",
        description: `${e.message ?? "请检查网络或联系开发人员"}`,
        variant: "destructive",
      });
    },
    onFinally: () => {
      setIsReset(false);
    },
  });

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [scrolled]);

  if (!isAuthenticated) return null;

  // Define the menu items based on user role
  const menuItems = [
    {
      to: "/resources",
      icon: <Image className="w-4 h-4" />,
      label: "素材库",
      requiredRole: "user",
    },
    {
      to: "/projects",
      icon: <Folders className="w-4 h-4" />,
      label: "我的项目",
      requiredRole: "user",
    },
    {
      to: "/knowledge",
      icon: <BookOpen className="w-4 h-4" />,
      label: "知识库",
      requiredRole: "user",
    },
    {
      to: "/development-guide",
      icon: <FileText className="w-4 h-4" />,
      label: "集成指南",
      requiredRole: "user",
    },
    {
      to: "/system-management",
      icon: <Settings className="w-4 h-4" />,
      label: "系统管理",
      requiredRole: "admin",
      children: [
        {
          to: "/system-management/users",
          icon: <Users className="w-4 h-4" />,
          label: "用户管理",
        },
        {
          to: "/system-management/resources",
          icon: <Server className="w-4 h-4" />,
          label: "资源管理",
        },
      ],
    },
  ];

  // Swap the positions of "知识库" and "我的项目"
  const swappedMenuItems = [...menuItems];
  // Find the indices of the items to swap
  const projectsIndex = swappedMenuItems.findIndex(
    (item) => item.label === "我的项目"
  );
  const knowledgeIndex = swappedMenuItems.findIndex(
    (item) => item.label === "知识库"
  );

  // Perform the swap
  if (projectsIndex !== -1 && knowledgeIndex !== -1) {
    [swappedMenuItems[projectsIndex], swappedMenuItems[knowledgeIndex]] = [
      swappedMenuItems[knowledgeIndex],
      swappedMenuItems[projectsIndex],
    ];
  }

  // Filter menu items based on user role
  const filteredMenuItems = swappedMenuItems.filter(
    (item) =>
      item.requiredRole === "user" || (isAdmin && item.requiredRole === "admin")
  );

  const changePwd = () => {
    setIsReset(true);
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    if (!newUser.oldPassword) {
      newErrors.password = "原密码不能为空";
    } else if (newUser.oldPassword.length < 6) {
      newErrors.password = "原密码长度不能少于6个字符";
    }

    if (!newUser.password) {
      newErrors.password = "密码不能为空";
    } else if (newUser.password.length < 6) {
      newErrors.password = "密码长度不能少于6个字符";
    }

    if (newUser.password !== newUser.confirmPassword) {
      newErrors.confirmPassword = "两次输入的密码不一致";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSaveUser = async () => {
    if (!validateForm()) return;
    if (store.get("user")) {
      const userInfo = JSON.parse(store.get("user"));
      if (userInfo.id) {
        run({
          id: userInfo.id,
          oldPassword: newUser.oldPassword,
          password: newUser.password,
        });
      } else {
        toast({
          title: "用户登录信息获取失败",
          description: `请检查网络或退出重新登录`,
          variant: "destructive",
        });
      }
    } else {
      toast({
        title: "用户登录信息获取失败",
        description: `请检查网络或退出重新登录`,
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setNewUser({
      oldPassword: "",
      password: "",
      confirmPassword: "",
    });
    setErrors({});
  };

  return (
    <header
      className={cn(
        "fixed top-0 left-0 right-0 z-30 transition-all duration-300",
        scrolled ? "bg-white/80 backdrop-blur-md shadow-sm" : "bg-transparent"
      )}
    >
      <div className="container flex items-center justify-between h-16 px-4 md:px-8">
        <div className="flex items-center gap-2">
          <Link to="/" className="text-xl font-semibold tracking-tight">
            智聆数字人平台
          </Link>
        </div>

        <nav className="hidden md:flex items-center space-x-1">
          {filteredMenuItems.map((item) =>
            item.children ? (
              <NavigationMenu key={item.to}>
                <NavigationMenuList>
                  <NavigationMenuItem>
                    <NavigationMenuTrigger
                      className={cn(
                        "px-3 py-2 text-sm font-medium transition-colors",
                        location.pathname.includes(item.to)
                          ? "text-primary"
                          : "text-muted-foreground hover:text-primary hover:bg-secondary/50"
                      )}
                    >
                      <div className="flex items-center gap-1.5">
                        {item.icon}
                        <span>{item.label}</span>
                      </div>
                    </NavigationMenuTrigger>
                    <NavigationMenuContent>
                      <ul className="grid w-[200px] gap-1 p-2">
                        {item.children.map((child) => (
                          <li key={child.to}>
                            <NavigationMenuLink asChild>
                              <Link
                                to={child.to}
                                className={cn(
                                  "block select-none space-y-1 rounded-md p-3 text-sm leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
                                  location.pathname === child.to
                                    ? "bg-accent text-accent-foreground"
                                    : ""
                                )}
                              >
                                <div className="flex items-center gap-2">
                                  {child.icon}
                                  <span>{child.label}</span>
                                </div>
                              </Link>
                            </NavigationMenuLink>
                          </li>
                        ))}
                      </ul>
                    </NavigationMenuContent>
                  </NavigationMenuItem>
                </NavigationMenuList>
              </NavigationMenu>
            ) : (
              <NavLink
                key={item.to}
                to={item.to}
                active={location.pathname.includes(item.to)}
              >
                <div className="flex items-center gap-1.5">
                  {item.icon}
                  <span>{item.label}</span>
                </div>
              </NavLink>
            )
          )}
        </nav>

        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="gap-2">
                <Avatar className="w-8 h-8">
                  <AvatarImage src="/placeholder.svg" />
                  <AvatarFallback className="bg-primary text-primary-foreground">
                    {user?.username?.slice(0, 2).toUpperCase() || "U"}
                  </AvatarFallback>
                </Avatar>
                <span className="hidden sm:inline-block">{user?.username}</span>
                <ChevronDown className="w-4 h-4 opacity-50" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              {filteredMenuItems.map((item) => {
                if (item.children) {
                  return (
                    <DropdownMenuSub key={item.to}>
                      <DropdownMenuSubTrigger className="md:hidden">
                        <div className="flex items-center gap-2">
                          {item.icon}
                          <span>{item.label}</span>
                        </div>
                      </DropdownMenuSubTrigger>
                      <DropdownMenuSubContent>
                        {item.children.map((child) => (
                          <DropdownMenuItem key={child.to} asChild>
                            <Link
                              to={child.to}
                              className="cursor-pointer flex items-center gap-2"
                            >
                              {child.icon}
                              <span>{child.label}</span>
                            </Link>
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuSubContent>
                    </DropdownMenuSub>
                  );
                }
                return (
                  <DropdownMenuItem key={item.to} className="md:hidden" asChild>
                    <Link
                      to={item.to}
                      className="cursor-pointer flex items-center gap-2"
                    >
                      {item.icon}
                      <span>{item.label}</span>
                    </Link>
                  </DropdownMenuItem>
                );
              })}
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={changePwd}>
                <Edit className="w-4 h-4 mr-2" />
                <span>修改密码</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-red-500 cursor-pointer"
                onClick={logout}
              >
                <LogOut className="w-4 h-4 mr-2" />
                <span>退出登录</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <Dialog
          open={isReset}
          onOpenChange={(open) => {
            if (!open) resetForm();
            setIsReset(open);
          }}
        >
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>修改密码</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="password" className="text-right">
                  原密码
                </Label>
                <div className="col-span-3 space-y-1">
                  <Input
                    type="password"
                    value={newUser.oldPassword}
                    onChange={(e) =>
                      setNewUser({ ...newUser, oldPassword: e.target.value })
                    }
                    className={errors.oldPassword ? "border-destructive" : ""}
                  />
                  {errors.oldPassword && (
                    <p className="text-sm text-destructive">
                      {errors.oldPassword}
                    </p>
                  )}
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="password" className="text-right">
                  密码
                </Label>
                <div className="col-span-3 space-y-1">
                  <Input
                    id="password"
                    type="password"
                    value={newUser.password}
                    onChange={(e) =>
                      setNewUser({ ...newUser, password: e.target.value })
                    }
                    className={errors.password ? "border-destructive" : ""}
                  />
                  {errors.password && (
                    <p className="text-sm text-destructive">
                      {errors.password}
                    </p>
                  )}
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="confirmPassword" className="text-right">
                  确认密码
                </Label>
                <div className="col-span-3 space-y-1">
                  <Input
                    id="confirmPassword"
                    type="password"
                    value={newUser.confirmPassword}
                    onChange={(e) =>
                      setNewUser({
                        ...newUser,
                        confirmPassword: e.target.value,
                      })
                    }
                    className={
                      errors.confirmPassword ? "border-destructive" : ""
                    }
                  />
                  {errors.confirmPassword && (
                    <p className="text-sm text-destructive">
                      {errors.confirmPassword}
                    </p>
                  )}
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  resetForm();
                  setIsReset(false);
                }}
              >
                取消
              </Button>
              <Button onClick={handleSaveUser}>修改</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </header>
  );
};

interface NavLinkProps {
  to: string;
  active: boolean;
  children: React.ReactNode;
}

const NavLink: React.FC<NavLinkProps> = ({ to, active, children }) => {
  return (
    <Link
      to={to}
      className={cn(
        "px-3 py-2 rounded-md text-sm font-medium transition-colors relative",
        active
          ? "text-primary"
          : "text-muted-foreground hover:text-primary hover:bg-secondary/50"
      )}
    >
      {children}
      {active && (
        <span className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1/2 h-0.5 bg-primary rounded-full" />
      )}
    </Link>
  );
};

export default Navbar;
