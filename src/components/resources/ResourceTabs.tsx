import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Image, Music } from "lucide-react";
import AvatarLibrary from "./AvatarLibrary";
import AudioLibrary from "./AudioLibrary";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

const ResourceTabs = () => {
  const [activeTab, setActiveTab] = useState("avatars");

  return (
    <Tabs
      defaultValue="avatars"
      className="w-full"
      onValueChange={setActiveTab}
    >
      <div className="mb-6 flex justify-center">
        <TabsList className="grid grid-cols-2 w-full max-w-md">
          <TabTrigger value="avatars" isActive={activeTab === "avatars"}>
            <Image className="w-4 h-4 mr-2" />
            <span>形象库</span>
          </TabTrigger>
          <TabTrigger value="audio" isActive={activeTab === "audio"}>
            <Music className="w-4 h-4 mr-2" />
            <span>音频库</span>
          </TabTrigger>
        </TabsList>
      </div>

      <TabsContent value="avatars" className="mt-0 px-1">
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <AvatarLibrary />
        </motion.div>
      </TabsContent>

      <TabsContent value="audio" className="mt-0 px-1">
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <AudioLibrary />
        </motion.div>
      </TabsContent>
    </Tabs>
  );
};

interface TabTriggerProps {
  value: string;
  isActive: boolean;
  children: React.ReactNode;
}

const TabTrigger: React.FC<TabTriggerProps> = ({
  value,
  isActive,
  children,
}) => {
  return (
    <TabsTrigger
      value={value}
      className={cn(
        "relative flex items-center justify-center gap-1 py-3 px-4",
        "data-[state=active]:bg-white data-[state=active]:shadow-sm"
      )}
    >
      {children}
      {isActive && (
        <motion.div
          layoutId="tab-indicator"
          className="absolute -bottom-[1px] left-0 right-0 h-[2px] bg-primary rounded-full mx-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        />
      )}
    </TabsTrigger>
  );
};

export default ResourceTabs;
