import { act, useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Upload,
  VideoIcon,
  ImageIcon,
  AlertCircle,
  Loader2,
  Play,
  Pause,
} from "lucide-react";

import ResourceCard from "@/components/ui/ResourceCard";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/components/ui/use-toast";
import { motion } from "framer-motion";
import { staggerContainer, staggerItem } from "@/lib/animations";
import { AClip, Clip, ClipList, Clips, DClip } from "@/request/clip";
import { useRequest } from "ahooks";
import type { Response } from "../vite-env";
import {
  Di<PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import LoadingDialog from "@/components/ui/LoadingDialog";

const AvatarLibrary = () => {
  const [activeTab, setActiveTab] = useState("video");
  const [selectedAvatar, setSelectedAvatar] = useState<string | null>(null);
  const [uploadedAvatars, setUploadedAvatars] = useState<Clip[]>([]);
  const [pageSize] = useState(10);
  const [pageNumber, setPageNumber] = useState(1);
  const [more, setMore] = useState(false);
  const [isPause, setIsPause] = useState(false);
  const [pauseUrl, setPauseUrl] = useState<string | null>(null);
  const [isPreview, setIsPreview] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);

  const videoRef = useRef<HTMLVideoElement>(null);
  const { toast } = useToast();

  const { run: runDelete } = useRequest(DClip, {
    manual: true,
    onSuccess: () => {
      toast({
        title: "删除已素材",
        description: `删除素材成功`,
      });
      setUploadedAvatars([]);
      if (pageNumber != 1) {
        setPageNumber(1);
      } else {
        setPageNumber(1);
        run({
          pageNumber: 1,
          pageSize,
          type: activeTab === "video" ? "Video" : "Image",
        });
      }
    },
    onError: (e) => {
      let message;
      if (e?.response?.data?.code == "10009") {
        message = "素材使用中，请先在项目列表中删除关联的项目";
      }
      toast({
        title: "删除素材失败",
        description: `${message || e.message || "请联系系统管理员"}`,
        variant: "destructive",
        duration: 5000,
      });
    },
    onFinally: () => {
      // Reset state
      setItemToDelete(null);
      setDeleteConfirmOpen(false);
    },
  });

  const changePreview = (path) => {
    setIsPreview(true);
    setPreviewUrl(path);
  };

  const changePause = (path) => {
    setIsPlaying(false);
    setIsPause(true);
    setPauseUrl(path);
  };
  const handlePlayToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isPlaying) {
      videoRef.current?.pause();
    } else {
      videoRef.current?.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleDeleteRequest = (id: string) => {
    setItemToDelete(id);
    setDeleteConfirmOpen(true);
  };

  const handleConfirmDelete = () => {
    if (itemToDelete) {
      runDelete(itemToDelete);
    }
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];

    const isVideo = activeTab === "video";
    const validTypes = isVideo
      ? ["video/mp4", "video/webm"]
      : ["image/jpeg", "image/png", "image/jpg"];

    if (!validTypes.includes(file.type)) {
      toast({
        title: "文件类型不支持",
        description: `请上传${
          isVideo ? "MP4或WEBM格式的视频文件" : "JPG或PNG格式的图片文件"
        }`,
        variant: "destructive",
      });
      return;
    }

    const filename = file.name;
    const regex = /^(.*?)(?=\.[^.]*$|$)/;
    const match = filename.match(regex);
    const nameWithoutExtension = match ? match : filename;
    const formData = new FormData();
    formData.append("file", file);
    formData.append(
      "name",
      nameWithoutExtension?.[0] || (nameWithoutExtension as string)
    );
    formData.append("category", "Customize");

    if (activeTab == "video") {
      formData.append("type", "Video");
      const url = URL.createObjectURL(file);
      const video = document.createElement("video");

      video.addEventListener("loadedmetadata", async () => {
        e.target.value = "";
        const width = video.videoWidth;
        const height = video.videoHeight;
        URL.revokeObjectURL(url); // 释放临时 URL
        if (width > height) {
          if (width <= 1920 && height <= 1080) {
            setUploading(true);
            try {
              await AClip(formData);
              toast({
                title: "上传成功",
                description: `${file.name} 已成功上传`,
              });
              setPageNumber(1);
              setUploadedAvatars([]);
              run({
                pageNumber,
                pageSize,
                type: activeTab === "video" ? "Video" : "Image",
              });
            } catch (e) {
              console.error(e);
              let message;
              if (e?.response?.data?.code == "10011") {
                message = "数字人形象复刻失败，请联系管理员处理";
              }
              toast({
                title: "上传失败",
                description: `${message ?? e.message ?? "请联系系统管理员"}`,
                variant: "destructive",
              });
            } finally {
              e.target.value = "";
              setUploading(false);
            }
          } else {
            toast({
              title: "视频分辨率不符合要求",
              description: `视频分辨率最大为1920*1080`,
              variant: "destructive",
            });
          }
        } else {
          if (width <= 1080 && height <= 1920) {
            setUploading(true);
            try {
              await AClip(formData);
              toast({
                title: "上传成功",
                description: `${file.name} 已成功上传`,
              });
              setPageNumber(1);
              setUploadedAvatars([]);
              run({
                pageNumber,
                pageSize,
                type: activeTab === "video" ? "Video" : "Image",
              });
            } catch (e) {
              console.error(e);
              let message;
              if (e?.response?.data?.code == "10011") {
                message = "数字人形象复刻失败，请联系管理员处理";
              }
              toast({
                title: "上传失败",
                description: `${message ?? e.message ?? "请联系系统管理员"}`,
                variant: "destructive",
              });
            } finally {
              e.target.value = "";
              setUploading(false);
            }
          } else {
            toast({
              title: "视频分辨率不符合要求",
              description: `视频分辨率最大为1920*1080`,
              variant: "destructive",
            });
          }
        }
      });
      video.src = url;
    } else {
      formData.append("type", "Image");
      const reader = new FileReader();
      reader.onload = (event) => {
        e.target.value = "";
        const image = new Image();
        image.src = event.target.result;
        image.onload = async () => {
          const width = image.naturalWidth; // 实际宽度
          const height = image.naturalHeight; // 实际高度
          if (width > height) {
            if (width <= 1920 && height <= 1080) {
              setUploading(true);
              try {
                await AClip(formData);
                toast({
                  title: "上传成功",
                  description: `${file.name} 已成功上传`,
                });
                setPageNumber(1);
                setUploadedAvatars([]);
                run({
                  pageNumber,
                  pageSize,
                  type: activeTab === "video" ? "Video" : "Image",
                });
              } catch (e) {
                console.error(e);
                let message;
                if (e?.response?.data?.code == "10011") {
                  message = "数字人形象复刻失败，请联系管理员处理";
                }
                toast({
                  title: "上传失败",
                  description: `${
                    message ?? e.message ?? "请联系系统管理员"
                  }`,
                  variant: "destructive",
                });
              } finally {
                e.target.value = "";
                setUploading(false);
              }
            } else {
              toast({
                title: "图片分辨率不符合要求",
                description: `图片分辨率最大为1920*1080`,
                variant: "destructive",
              });
            }
          } else {
            if (width <= 1080 && height <= 1920) {
              setUploading(true);
              try {
                await AClip(formData);
                toast({
                  title: "上传成功",
                  description: `${file.name} 已成功上传`,
                });
                setPageNumber(1);
                setUploadedAvatars([]);
                run({
                  pageNumber,
                  pageSize,
                  type: activeTab === "video" ? "Video" : "Image",
                });
              } catch (e) {
                console.error(e);
                let message;
                if (e?.response?.data?.code == "10011") {
                  message = "数字人形象复刻失败，请联系管理员处理";
                }
                toast({
                  title: "上传失败",
                  description: `${
                    message ?? e.message ?? "请联系系统管理员"
                  }`,
                  variant: "destructive",
                });
              } finally {
                e.target.value = "";
                setUploading(false);
              }
            } else {
              toast({
                title: "图片分辨率不符合要求",
                description: `图片分辨率最大为1920*1080`,
                variant: "destructive",
              });
            }
          }
        };
        image.onerror = () => {
          toast({
            title: "加载图片失败",
            description: `加载图片失败`,
            variant: "destructive",
          });
        };
      };
      reader.readAsDataURL(file);
    }
    return;
  };

  const handleSelect = (id: string) => {
    setSelectedAvatar(id === selectedAvatar ? null : id);
  };
  const { run, loading: isLoading } = useRequest(Clips, {
    manual: true,
    onSuccess: (data: Response<ClipList>) => {
      if (data.data.items.length + uploadedAvatars.length >= data.data.total) {
        setMore(false);
      } else {
        setMore(true);
      }
      setUploadedAvatars([...uploadedAvatars, ...data.data.items]);
    },
    onError: (e, params) => {
      toast({
        title: "素材获取失败",
        description: `${e.message || "请联系系统管理员"}`,
        variant: "destructive",
        duration: 5000,
      });
    },
  });
  useEffect(() => {
    setUploadedAvatars([]);
    setPageNumber(1);
    run({
      pageNumber,
      pageSize,
      type: activeTab === "video" ? "Video" : "Image",
    });
  }, [activeTab]);
  useEffect(() => {
    run({
      pageNumber,
      pageSize,
      type: activeTab === "video" ? "Video" : "Image",
    });
  }, [pageNumber]);

  return (
    <div className="space-y-6">
      <LoadingDialog
        open={uploading}
        message={`${activeTab === "video" ? "视频" : "图片"}形象复刻中，请稍候...`}
      />
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="w-full max-w-md grid grid-cols-2 mb-6">
          <TabsTrigger value="video" className="flex items-center gap-1.5">
            <VideoIcon className="w-4 h-4" />
            <span>视频形象</span>
          </TabsTrigger>
          <TabsTrigger value="image" className="flex items-center gap-1.5">
            <ImageIcon className="w-4 h-4" />
            <span>图片形象</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="video" className="mt-0">
          <UploadCard type="Video" onUpload={handleFileUpload} />
        </TabsContent>

        <TabsContent value="image" className="mt-0">
          <UploadCard type="Image" onUpload={handleFileUpload} />
        </TabsContent>
      </Tabs>

      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
            <p className="text-muted-foreground">正在加载资源...</p>
          </div>
        </div>
      ) : uploadedAvatars.length > 0 ? (
        <>
          <motion.div
            variants={staggerContainer}
            initial="initial"
            animate="animate"
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          >
            {uploadedAvatars.map((avatar) => (
              <motion.div key={avatar.id} variants={staggerItem}>
                <ResourceCard
                  {...avatar}
                  isChoice={false}
                  isSelected={avatar.id === selectedAvatar}
                  onSelect={handleSelect}
                  onPause={changePause}
                  onPreview={changePreview}
                  onDelete={handleDeleteRequest}
                />
              </motion.div>
            ))}
          </motion.div>
          {more && (
            <div
              style={{ cursor: "pointer" }}
              onClick={() => setPageNumber(pageNumber + 1)}
            >
              加载更多
            </div>
          )}
        </>
      ) : (
        <Alert className="bg-muted/50 border-dashed">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            没有{activeTab === "video" ? "视频" : "图片"}
            形象，请上传或使用演示形象
          </AlertDescription>
        </Alert>
      )}

      <AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除此形象资源吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setItemToDelete(null)}>
              取消
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              className="bg-destructive text-destructive-foreground"
            >
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Dialog open={isPause} onOpenChange={setIsPause}>
        <DialogContent>
          <video
            style={{ maxHeight: "800px", maxWidth: "1000px", padding: "10px" }}
            ref={videoRef}
            src={pauseUrl}
            className="w-full h-full object-cover"
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            onEnded={() => setIsPlaying(false)}
          />
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 flex items-center justify-center"
          >
            <Button
              size="icon"
              variant="secondary"
              className="rounded-full h-10 w-10 text-primary bg-white hover:bg-white hover:text-accent-foreground"
              onClick={handlePlayToggle}
            >
              {isPlaying ? (
                <Pause className="h-5 w-5" />
              ) : (
                <Play className="h-5 w-5 ml-0.5" />
              )}
            </Button>
          </motion.div>
        </DialogContent>
      </Dialog>

      <Dialog open={isPreview} onOpenChange={setIsPreview}>
        <DialogContent>
          <img
            src={previewUrl}
            alt={""}
            className="w-full h-full object-fit"
            style={{ padding: "10px", maxHeight: "800px", maxWidth: "1000px" }}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

interface UploadCardProps {
  type: string;
  onUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const UploadCard = ({ type, onUpload }: UploadCardProps) => {
  return (
    <Card className="mb-8 border-dashed">
      <CardContent className="p-6">
        <div className="flex flex-col items-center justify-center gap-4">
          <div className="rounded-full bg-muted p-3">
            <Upload className="h-6 w-6 text-primary" />
          </div>
          <div className="text-center space-y-1.5">
            <h3 className="font-semibold text-lg">
              上传{type === "Video" ? "视频" : "图片"}形象
            </h3>
            <p className="text-sm text-muted-foreground">
              {type === "Video"
                ? "支持MP4、WEBM格式，最大文件大小50MB，视频分辨率最大支持1920*1080，视频时长建议15秒以内"
                : "支持JPG、PNG格式，最大文件大小10MB，图片分辨率最大支持1920*1080"}
            </p>
          </div>
          <div className="mt-2 w-full max-w-sm">
            <div className="flex flex-col gap-1.5">
              <Label htmlFor={`${type}-upload`} className="sr-only">
                选择文件
              </Label>
              <Input
                id={`${type}-upload`}
                type="file"
                accept={
                  type === "Video"
                    ? "video/mp4,video/webm"
                    : "image/jpeg,image/png,image/jpg"
                }
                onChange={onUpload}
                className="cursor-pointer"
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AvatarLibrary;
