import { useEffect, useMemo, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Upload, AlertCircle, Loader2 } from "lucide-react";
import AudioCard from "@/components/ui/AudioCard";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/components/ui/use-toast";
import { motion } from "framer-motion";
import { staggerContainer, staggerItem } from "@/lib/animations";
import { AClip, Clips, type Clip, type ClipList, DClip } from "@/request/clip";
import { useRequest } from "ahooks";
import type { Response } from "../vite-env";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import LoadingDialog from "@/components/ui/LoadingDialog";

const AudioLibrary = () => {
  const [selectedAudio, setSelectedAudio] = useState<string | null>(null);
  const [uploadedAudios, setUploadedAudios] = useState<Clip[]>([]);
  const [systemAudios, setSystemAudios] = useState<Clip[]>([]);
  const { toast } = useToast();
  const [pageSize] = useState(10);
  const [pageNumber, setPageNumber] = useState(1);
  const [customPageSize] = useState(10);
  const [customPageNumber, setCustomPageNumber] = useState(1);
  const [uploadMore, setUploadMore] = useState(false);
  const [systemMore, setSystemMore] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);

  const {
    run: runUploadList,
    loading: isLoading1,
    refresh: refreshUpload,
  } = useRequest(Clips, {
    manual: true,
    defaultParams: [
      {
        type: "Audio",
        pageNumber: customPageNumber,
        pageSize: customPageSize,
        category: "Customize",
      },
    ],
    onSuccess: (data: Response<ClipList>) => {
      if (data.data.items.length + uploadedAudios.length >= data.data.total) {
        setUploadMore(false);
      } else {
        setUploadMore(true);
      }
      setUploadedAudios([...uploadedAudios, ...data.data.items]);
    },
    onError: (e, params) => {
      toast({
        title: "自定义素材获取失败",
        description: `${e.message || "请联系系统管理员"}`,
        variant: "destructive",
        duration: 5000,
      });
    },
  });
  const {
    run: runSystemList,
    loading: isLoading2,
    refresh: refreshSytem,
  } = useRequest(Clips, {
    manual: true,
    defaultParams: [
      { type: "Audio", pageNumber, pageSize, category: "System" },
    ],
    onSuccess: (data: Response<ClipList>) => {
      if (data.data.items.length < pageSize) {
        setSystemMore(false);
      } else {
        setSystemMore(true);
      }
      setSystemAudios([...systemAudios, ...data.data.items]);
    },
    onError: (e, params) => {
      toast({
        title: "系统素材获取失败",
        description: `${e.message || "请联系系统管理员"}`,
        variant: "destructive",
        duration: 5000,
      });
    },
  });

  const { run: runDelete } = useRequest(DClip, {
    manual: true,
    onSuccess: () => {
      toast({
        title: "删除已素材",
        description: `删除素材成功`,
      });
      setUploadedAudios([]);
      if (customPageNumber != 1) {
        setCustomPageNumber(1);
      } else {
        setCustomPageNumber(1);
        runUploadList({
          type: "Audio",
          pageNumber: 1,
          pageSize: customPageSize,
          category: "Customize",
        });
      }
    },
    onError: (e) => {
      let message;
      if (e?.response?.data?.code == "10009") {
        message = "素材使用中，请先在项目列表中删除关联的项目";
      }
      toast({
        title: "删除素材失败",
        description: `${message || e.message || "请联系系统管理员"}`,
        variant: "destructive",
        duration: 5000,
      });
    },
    onFinally: () => {
      setItemToDelete(null);
      setDeleteConfirmOpen(false);
    },
  });
  const isLoading = useMemo(() => {
    return isLoading1 && isLoading2;
  }, [isLoading1, isLoading2]);

  const handleDeleteRequest = (id: string) => {
    setItemToDelete(id);
    setDeleteConfirmOpen(true);
  };

  const handleConfirmDelete = () => {
    if (itemToDelete) {
      // Reset state
      runDelete(itemToDelete);
    }
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    const validTypes = ["audio/mp3", "audio/mpeg", "audio/wav"];

    if (!validTypes.includes(file.type)) {
      toast({
        title: "文件类型不支持",
        description: "请上传MP3或WAV格式的音频文件",
        variant: "destructive",
      });
      return;
    }

    const filename = file.name;
    const regex = /^(.*?)(?=\.[^.]*$|$)/;
    const match = filename.match(regex);
    const nameWithoutExtension = match ? match : filename;
    const formData = new FormData();
    formData.append("file", file);
    formData.append(
      "name",
      nameWithoutExtension?.[0] || (nameWithoutExtension as string)
    );
    formData.append("category", "Customize");

    formData.append("type", "Audio");
    setUploading(true);

    try {
      await AClip(formData);
      toast({
        title: "上传成功",
        description: `${file.name} 已成功上传`,
      });
      setUploadedAudios([]);
      setCustomPageNumber(1);
      runUploadList({
        type: "Audio",
        pageNumber: customPageNumber,
        pageSize: customPageSize,
        category: "Customize",
      });
    } catch (e) {
      console.error(e);
      let message;
      if (e?.response?.data?.code == "10011") {
        message = "数字人音频复刻失败，请联系系统管理员处理";
      }
      toast({
        title: "上传失败",
        description: `${message ?? e.message ?? "请联系系统管理员"}`,
        variant: "destructive",
      });
    } finally {
      e.target.value = "";
      setUploading(false);
    }
  };

  const handleSelect = (id: string) => {
    setSelectedAudio(id === selectedAudio ? null : id);
  };

  useEffect(() => {
    runSystemList({ type: "Audio", pageNumber, pageSize, category: "System" });
  }, [pageNumber]);
  useEffect(() => {
    runUploadList({
      type: "Audio",
      pageNumber: customPageNumber,
      pageSize: customPageSize,
      category: "Customize",
    });
  }, [customPageNumber]);

  return (
    <div className="space-y-6">
      <LoadingDialog open={uploading} message="正在上传音频文件..." />
      <Card className="mb-8 border-dashed">
        <CardContent className="p-6">
          <div className="flex flex-col items-center justify-center gap-4">
            <div className="rounded-full bg-muted p-3">
              <Upload className="h-6 w-6 text-primary" />
            </div>
            <div className="text-center space-y-1.5">
              <h3 className="font-semibold text-lg">上传音频文件</h3>
              <p className="text-sm text-muted-foreground">
                支持MP3、WAV格式，最大文件大小20MB，音频时长建议15秒以内
              </p>
            </div>
            <div className="mt-2 w-full max-w-sm">
              <div className="flex flex-col gap-1.5">
                <Label htmlFor="audio-upload" className="sr-only">
                  选择文件
                </Label>
                <Input
                  id="audio-upload"
                  type="file"
                  accept="audio/mp3,audio/mpeg,audio/wav"
                  onChange={handleFileUpload}
                  className="cursor-pointer"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
            <p className="text-muted-foreground">正在加载资源...</p>
          </div>
        </div>
      ) : (
        <>
          <div className="mb-6">
            <h2 className="text-lg font-semibold mb-4">自定义音频</h2>

            {uploadedAudios.length > 0 ? (
              <motion.div
                variants={staggerContainer}
                initial="initial"
                animate="animate"
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
              >
                {uploadedAudios.map((audio) => (
                  <motion.div key={audio.id} variants={staggerItem}>
                    <AudioCard
                      {...audio}
                      isChoice={false}
                      isSelected={audio.id === selectedAudio}
                      onSelect={handleSelect}
                      onDelete={handleDeleteRequest}
                    />
                  </motion.div>
                ))}
              </motion.div>
            ) : (
              <Alert className="bg-muted/50 border-dashed">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  没有自定义音频，请上传音频文件
                </AlertDescription>
              </Alert>
            )}
          </div>
          {uploadMore && (
            <div
              style={{ cursor: "pointer" }}
              onClick={() => setCustomPageNumber(customPageNumber + 1)}
            >
              加载更多
            </div>
          )}

          <div>
            <h2 className="text-lg font-semibold mb-4">系统音频</h2>
            <motion.div
              variants={staggerContainer}
              initial="initial"
              animate="animate"
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
            >
              {systemAudios.map((audio) => (
                <motion.div key={audio.id} variants={staggerItem}>
                  <AudioCard
                    {...audio}
                    isSelected={audio.id === selectedAudio}
                    onSelect={handleSelect}
                  />
                </motion.div>
              ))}
            </motion.div>
          </div>
          {systemMore && (
            <div
              style={{ cursor: "pointer" }}
              onClick={() => setPageNumber(pageNumber + 1)}
            >
              加载更多
            </div>
          )}

          <AlertDialog
            open={deleteConfirmOpen}
            onOpenChange={setDeleteConfirmOpen}
          >
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>确认删除</AlertDialogTitle>
                <AlertDialogDescription>
                  您确定要删除此音频资源吗？此操作无法撤销。
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel onClick={() => setItemToDelete(null)}>
                  取消
                </AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleConfirmDelete}
                  className="bg-destructive text-destructive-foreground"
                >
                  删除
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </>
      )}
    </div>
  );
};

export default AudioLibrary;
