import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { DialogFooter } from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  VideoIcon,
  ImageIcon,
  Music,
  FileText,
  BookOpen,
  Cpu,
  AlertCircle,
} from "lucide-react";
import ResourceCard from "@/components/ui/ResourceCard";
import AudioCard from "@/components/ui/AudioCard";
import KnowledgeCard from "@/components/ui/KnowledgeCard";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Clip } from "@/request/clip";
import {
  AllVideo,
  AllImage,
  AllAudio,
  AllKnowledge,
  Project,
} from "@/request/project";
import type { Response } from "../vite-env";
import { useRequest } from "ahooks";
import { useToast } from "@/components/ui/use-toast";
import { Knowledge } from "@/request/knowledge";

interface CreateProjectProps {
  onSubmit: (project: any) => void;
  editingProject: Partial<Project> | null;
  projects: any[];
}

const CreateProject = ({
  onSubmit,
  editingProject,
  projects,
}: CreateProjectProps) => {
  const [name, setName] = useState("");
  const [avatarType, setAvatarType] = useState<"Video" | "Image">("Video");
  const [selectedAvatar, setSelectedAvatar] = useState<string>("");
  const [selectedAudio, setSelectedAudio] = useState<string>("");
  const [selectedKnowledgeBase, setSelectedKnowledgeBase] =
    useState<string>("");

  const [step, setStep] = useState(1);

  const [videos, setVideos] = useState<Clip[]>([]);
  const [images, setImages] = useState<Clip[]>([]);
  const [audios, setAudios] = useState<Clip[]>([]);
  const [knowledges, setKnowledges] = useState<Knowledge[]>([]);
  const { toast } = useToast();

  const { run: runVideo } = useRequest(AllVideo, {
    onSuccess: (data: Response<Clip[]>) => {
      setVideos(data.data);
    },
    onError: (e) => {
      toast({
        title: "素材获取失败",
        description: `${e.message || "视频获取失败,请联系系统管理员"}`,
        variant: "destructive",
      });
    },
  });

  const { run: runImage } = useRequest(AllImage, {
    onSuccess: (data: Response<Clip[]>) => {
      console.log("======image", data.data);
      setImages(data.data);
    },
    onError: (e) => {
      toast({
        title: "素材获取失败",
        description: `${e.message || "图片获取失败,请联系系统管理员"}`,
        variant: "destructive",
      });
    },
  });

  const { run: runAudio } = useRequest(AllAudio, {
    onSuccess: (data: Response<Clip[]>) => {
      setAudios(data.data);
    },
    onError: (e) => {
      toast({
        title: "素材获取失败",
        description: `${e.message || "音频获取失败,请联系系统管理员"}`,
        variant: "destructive",
      });
    },
  });
  const { run: runKnowledge } = useRequest(AllKnowledge, {
    onSuccess: (data: Response<Knowledge[]>) => {
      setKnowledges(data.data);
    },
    onError: (e) => {
      toast({
        title: "知识库获取失败",
        description: `${e.message || "音频获取失败,请联系系统管理员"}`,
        variant: "destructive",
      });
    },
  });

  useEffect(() => {
    if (editingProject) {
      setName(editingProject.name);
      setAvatarType(editingProject.type);
      setSelectedAvatar(editingProject.videoClipId);
      setSelectedAudio(editingProject.audioClipId);
      setSelectedKnowledgeBase(editingProject.knowledgeId);
    }
    //       setAvatarType(project.avatar.type);
    //       setSelectedAvatar(project.avatar.id);
    //       setSelectedAudio(project.audio.id);
    //       if (project.knowledgeBase) {
    //         setSelectedKnowledgeBase(project.knowledgeBase.id);
    //       }
    //       if (project.model) {
    //         setSelectedModel(project.model.id);
    //       }
  }, [editingProject]);

  // useEffect(() => {
  //   if (editingProjectId) {
  //     const project = projects.find((p) => p.id === editingProjectId);
  //     if (project) {
  //       setName(project.title);
  //       setAvatarType(project.avatar.type);
  //       setSelectedAvatar(project.avatar.id);
  //       setSelectedAudio(project.audio.id);
  //       if (project.knowledgeBase) {
  //         setSelectedKnowledgeBase(project.knowledgeBase.id);
  //       }
  //       if (project.model) {
  //         setSelectedModel(project.model.id);
  //       }
  //     }
  //   }
  // }, [editingProjectId, projects]);

  const handleSubmit = () => {
    const newProject: Partial<Project> = {
      id: editingProject ? editingProject.id : null,
      name,
      videoClipId: selectedAvatar,
      audioClipId: selectedAudio,
      knowledgeId: selectedKnowledgeBase,
      type: avatarType,
    };

    onSubmit(newProject);
  };

  return (
    <div className="space-y-6 py-2">
      {step === 1 && (
        <div className="space-y-4">
          <div>
            <Label htmlFor="project-title">项目名称</Label>
            <Input
              id="project-title"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="请输入项目名称"
              className="mt-1.5"
            />
          </div>

          <div className="space-y-2">
            <Label>选择形象类型</Label>
            <RadioGroup
              value={avatarType}
              onValueChange={(value) =>
                setAvatarType(value as "Video" | "Image")
              }
              className="flex gap-4 mt-1.5"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="Video" id="video" />
                <Label
                  htmlFor="Video"
                  className="cursor-pointer flex items-center gap-1.5"
                >
                  <VideoIcon className="h-4 w-4" />
                  视频形象
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="Image" id="image" />
                <Label
                  htmlFor="image"
                  className="cursor-pointer flex items-center gap-1.5"
                >
                  <ImageIcon className="h-4 w-4" />
                  图片形象
                </Label>
              </div>
            </RadioGroup>
          </div>

          <div className="space-y-3">
            <Label>选择{avatarType === "Video" ? "视频" : "图片"}形象</Label>
            <div
              className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-1.5"
              style={{ maxHeight: "350px", overflow: "auto" }}
            >
              {(avatarType === "Video" ? videos : images).map((avatar) => (
                <ResourceCard
                  key={avatar.id}
                  {...avatar}
                  isChoice={true}
                  isSelected={avatar.id === selectedAvatar}
                  onSelect={setSelectedAvatar}
                />
              ))}
            </div>
          </div>
        </div>
      )}

      {step === 2 && (
        <div className="space-y-4">
          <div className="space-y-3">
            <Label>选择音频</Label>
            <div
              className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-1.5"
              style={{ maxHeight: "350px", overflow: "auto" }}
            >
              {audios.map((audio) => (
                <AudioCard
                  key={audio.id}
                  {...audio}
                  isChoice={true}
                  isSelected={audio.id === selectedAudio}
                  onSelect={setSelectedAudio}
                />
              ))}
            </div>
          </div>
        </div>
      )}

      {step === 3 && (
        <div className="space-y-4">
          <div className="space-y-3">
            <Label>选择知识库</Label>
            <div
              className="grid grid-cols-1 gap-4 mt-1.5"
              style={{ maxHeight: "350px", overflow: "auto" }}
            >
              {knowledges.length > 0 ? (
                knowledges.map((knowledgeBase) => (
                  <div key={knowledgeBase.id} className="relative">
                    <input
                      type="radio"
                      id={`kb-${knowledgeBase.id}`}
                      className="peer sr-only"
                      checked={knowledgeBase.id === selectedKnowledgeBase}
                      onChange={() =>
                        setSelectedKnowledgeBase(knowledgeBase.id)
                      }
                    />
                    <label
                      htmlFor={`kb-${knowledgeBase.id}`}
                      className="block cursor-pointer"
                    >
                      <div className="absolute -inset-1 rounded-lg border-2 border-transparent peer-checked:border-primary transition-all duration-200"></div>
                      <KnowledgeCard
                        isChoice={true}
                        isSelected={knowledgeBase.id === selectedKnowledgeBase}
                        onSelect={setSelectedKnowledgeBase}
                        {...knowledgeBase}
                        onEdit={undefined}
                        onDelete={undefined}
                      />
                    </label>
                  </div>
                ))
              ) : (
                <Alert className="bg-muted/50 border-dashed">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    还没有创建任何知识库，请先前往知识库页面创建
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </div>
        </div>
      )}

      <DialogFooter className="flex justify-between sm:justify-between pt-4">
        {step === 1 ? (
          <>
            <div></div>
            <Button
              type="button"
              onClick={() => setStep(2)}
              disabled={!name || !selectedAvatar}
            >
              下一步
            </Button>
          </>
        ) : step === 2 ? (
          <>
            <Button type="button" variant="outline" onClick={() => setStep(1)}>
              上一步
            </Button>
            <Button
              type="button"
              onClick={() => setStep(3)}
              disabled={!selectedAudio}
            >
              下一步
            </Button>
          </>
        ) : (
          <>
            <Button type="button" variant="outline" onClick={() => setStep(2)}>
              上一步
            </Button>
            <Button
              type="button"
              onClick={handleSubmit}
              disabled={
                !name ||
                !selectedAvatar ||
                !selectedAudio ||
                !selectedKnowledgeBase
              }
            >
              {editingProject ? "保存更改" : "创建项目"}
            </Button>
          </>
        )}
      </DialogFooter>
    </div>
  );
};

export default CreateProject;
