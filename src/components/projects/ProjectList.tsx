import { useState, useEffect } from "react";
import {
  PlusCircle,
  Search,
  FilterX,
  AlertCircle,
  Trash,
  AlertTriangle,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import ProjectCard from "@/components/ui/ProjectCard";
import { motion } from "framer-motion";
import { staggerContainer, staggerItem } from "@/lib/animations";
import { useToast } from "@/components/ui/use-toast";
import axios from "axios";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import CreateProject from "./CreateProject";
import {
  AProject,
  type Project as ProjectFormat,
  Projects,
  UProject,
  DProject,
  type ProjectList,
} from "@/request/project";
import { useRequest } from "ahooks";
import type { Response } from "../vite-env";
import debounce from "lodash/debounce";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

const ProjectList = () => {
  const [projects, setProjects] = useState<ProjectFormat[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  const [editingProject, setEditingProject] =
    useState<Partial<ProjectFormat> | null>(null);
  const [pageSize] = useState(10);
  const [pageNumber, setPageNumber] = useState(1);
  const [more, setMore] = useState(false);
  const { toast } = useToast();

  const [projectToDelete, setProjectToDelete] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [projectTitleToDelete, setProjectTitleToDelete] = useState("");

  const { run: runAdd } = useRequest(AProject, {
    manual: true,
    onSuccess: () => {
      setProjects([]);
      toast({
        title: "项目已创建",
        description: `项目创建成功`,
      });
      if (pageNumber == 1) {
        runAll({ pageSize, pageNumber: 1 });
      } else {
        setPageNumber(1);
      }
    },
    onError: (e) => {
      toast({
        title: "项目创建失败",
        description: `${e.message ?? "请检查网络或联系系统管理员"}`,
        variant: "destructive",
      });
    },
    onFinally: () => {
      setIsCreating(false);
    },
  });
  const { run: runUpdate } = useRequest(UProject, {
    manual: true,
    onSuccess: (data: Response<ProjectFormat>) => {
      setProjects((prev: ProjectFormat[]) => {
        return prev.map((item) => {
          if (item.id == data.data.id) {
            return data.data;
          }
          return item;
        });
      });
      toast({
        title: "项目已修改",
        description: `项目修改成功`,
      });
    },
    onError: (e) => {
      toast({
        title: "项目修改失败",
        description: `${e.message ?? "请检查网络或联系系统管理员"}`,
        variant: "destructive",
      });
    },
    onFinally: () => {
      setIsCreating(false);
      setEditingProject(null);
    },
  });

  const { run: runAll, loading: isLoading } = useRequest(Projects, {
    manual: true,
    onSuccess: (data: Response<ProjectList>) => {
      if (projects.length + data.data.items.length >= data.data.total) {
        setMore(false);
      } else {
        setMore(true);
      }
      setProjects([...data.data.items, ...projects]);
    },
  });

  const { run: runDelete } = useRequest(DProject, {
    manual: true,
    onSuccess: () => {
      setProjects([]);
      toast({
        title: "项目已删除",
        description: `项目删除成功`,
      });
      if (pageNumber == 1) {
        runAll({ pageSize, pageNumber: 1 });
      } else {
        setPageNumber(1);
      }
    },
    onError: (e) => {
      toast({
        title: "项目删除失败",
        description: `${e.message ?? "请检查网络或联系系统管理员"}`,
        variant: "destructive",
      });
    },
    onFinally: () => {
      setDeleteDialogOpen(false);
      setProjectToDelete(null);
    },
  });

  useEffect(() => {
    if (searchTerm) {
      runAll({ pageNumber, pageSize, name: searchTerm });
    } else {
      runAll({ pageNumber, pageSize });
    }
  }, [pageNumber, searchTerm]);

  const handleCreateProject = async (newProject: Partial<ProjectFormat>) => {
    if (editingProject) {
      runUpdate(newProject);
    } else {
      runAdd(newProject);
    }
  };

  const handleEditProject = (project: Partial<ProjectFormat>) => {
    setEditingProject(project);
    setIsCreating(true);
  };

  const handleDeleteProject = async (projectId: string) => {
    const projectToDelete = projects.find((p) => p.id === projectId);
    if (projectToDelete) {
      setProjectTitleToDelete(projectToDelete.name);
      setProjectToDelete(projectId);
      setDeleteDialogOpen(true);
    }
  };

  const handleClearSearch = () => {
    setSearchTerm("");
    setProjects([]);
    setPageNumber(1);
  };
  const search = (e: any) => {
    setProjects([]);
    setPageNumber(1);
    const keyword = e.target.value;
    setSearchTerm(keyword.trim());
  };

  const handleDeleteConfirm = async () => {
    if (!projectToDelete) return;
    runDelete(projectToDelete);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <h1 className="text-2xl font-semibold">我的项目</h1>
        <Button
          onClick={() => {
            setEditingProject(null);
            setIsCreating(true);
          }}
          className="gap-1"
        >
          <PlusCircle className="h-4 w-4" />
          创建新项目
        </Button>
      </div>

      <div className="relative">
        <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="搜索项目..."
          value={searchTerm}
          onChange={search}
          className="pl-9 pr-9"
        />
        {searchTerm && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-1 top-1 h-8 w-8 text-muted-foreground"
            onClick={handleClearSearch}
          >
            <FilterX className="h-4 w-4" />
          </Button>
        )}
      </div>

      {isLoading ? (
        <div className="flex justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : projects.length > 0 ? (
        <>
          <motion.div
            variants={staggerContainer}
            initial="initial"
            animate="animate"
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          >
            {projects.map((project) => (
              <motion.div key={project.id} variants={staggerItem}>
                <ProjectCard
                  {...project}
                  onEdit={handleEditProject}
                  onDelete={handleDeleteProject}
                />
              </motion.div>
            ))}
          </motion.div>
          {more && (
            <div
              style={{ cursor: "pointer" }}
              onClick={() => setPageNumber(pageNumber + 1)}
            >
              加载更多
            </div>
          )}
        </>
      ) : searchTerm ? (
        <Alert className="bg-muted/50 border-dashed">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            没有找到与 "{searchTerm}" 相关的项目
          </AlertDescription>
        </Alert>
      ) : (
        <Alert className="bg-muted/50 border-dashed">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            没有项目，点击"创建新项目"开始创建
          </AlertDescription>
        </Alert>
      )}

      <Dialog open={isCreating} onOpenChange={setIsCreating}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {editingProject ? "编辑项目" : "创建新项目"}
            </DialogTitle>
            <DialogDescription>
              {editingProject
                ? "修改项目信息和所使用的素材"
                : "创建一个新的数字人项目，选择形象和音频"}
            </DialogDescription>
          </DialogHeader>
          <CreateProject
            onSubmit={handleCreateProject}
            editingProject={editingProject}
            projects={projects}
          />
        </DialogContent>
      </Dialog>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              确认删除项目
            </AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除项目{" "}
              <span className="font-semibold">"{projectTitleToDelete}"</span>{" "}
              吗？此操作不可撤销，删除后数据将无法恢复。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              <Trash className="h-4 w-4 mr-2" />
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default ProjectList;
