import axios from "./index";

// 创建聊天会话接口
export const createChatSession = async (): Promise<string> => {
  const response = await axios.post("http://localhost:10070/v1/session");
  return response.data;
};

// 聊天接口参数类型
export interface ChatRequest {
  knowledgeIds: string[];
  question: string;
  sessionId: string;
}

// 聊天响应数据类型
export interface ChatResponse {
  code: number;
  message: string;
  data: {
    answer: string;
    reference: Record<string, any>;
    param: Array<{
      key: string;
      name: string;
      optional: boolean;
      type: string;
    }>;
    id: string;
    session_id: string;
  };
}

// 聊天接口 - 使用SSE流式接收
export const sendChatMessage = async (
  params: ChatRequest,
  onMessage: (data: ChatResponse) => void,
  onError: (error: Error) => void,
  onComplete: () => void
): Promise<void> => {
  try {
    const response = await fetch("http://localhost:10070/v1/chat", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    const decoder = new TextDecoder();

    if (!reader) {
      throw new Error("Response body is not readable");
    }

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        onComplete();
        break;
      }

      const chunk = decoder.decode(value, { stream: true });
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (line.trim() === '') continue;
        
        try {
          // 尝试解析JSON数据
          const data = JSON.parse(line);
          onMessage(data);
        } catch (parseError) {
          // 如果不是JSON格式，可能是SSE格式
          if (line.startsWith('data: ')) {
            try {
              const jsonData = JSON.parse(line.substring(6));
              onMessage(jsonData);
            } catch (sseParseError) {
              console.warn('Failed to parse SSE data:', line);
            }
          }
        }
      }
    }
  } catch (error) {
    onError(error as Error);
  }
};
