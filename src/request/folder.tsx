import axios from "./index";
import type { Response } from "../vite-env";

export interface Folder {
  id?: string;
  name: string;
  createTime?: string;
  updateTime?: string;
  parentId?: string;
  knowledgeId?: string;
}
export interface FolderWithParent extends Folder {
  parent: Folder;
}
export function folders(knowledgeId: string, parentId?: string) {
  return axios<Response<Folder[]>>(`/v1/folders`, {
    method: "get",
    params: { knowledgeId, parentId },
  });
}
export function AFolder(data: Partial<Folder>) {
  return axios<Response<Folder>>(`/v1/folders`, {
    method: "post",
    data,
  });
}
export function DFolder(folderId: string) {
  return axios<Response<Folder>>(`/v1/folders/${folderId}`, {
    method: "delete",
  });
}
export function UFolder(data: Partial<Folder>) {
  const { id, ...res } = data;
  return axios<Response<Folder>>(`/v1/folders/${id}`, {
    method: "patch",
    data: { ...res },
  });
}
export function PFolder(knowledgeBaseId: string, parentId: string) {
  return axios<Response<FolderWithParent[]>>(`/v1/folders`, {
    method: "get",
    params: { knowledgeId: knowledgeBaseId, parentId },
  });
}
