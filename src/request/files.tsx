import axios from "./index";
import type { Response } from "../vite-env";

export type FileFormat = {
  id?: string;
  name: string;
  size: string;
  path: string;
  mimeType: string;
  createTime?: string;
  knowledgeId?: string;
  folderId?: string;
};
export function files(knowledgeId: string, folderId?: string) {
  return axios<Response<FileFormat[]>>(`/v1/files`, {
    method: "get",
    params: { knowledgeId, folderId },
  });
}
export function AFile(formData: any) {
  return axios.post(`/v1/files`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

export function DFile(fileId: string) {
  return axios<Response<FileFormat>>(`/v1/files/${fileId}`, {
    method: "delete",
  });
}

export function UFile(data: Partial<FileFormat>) {
  const { id, ...res } = data;
  return axios<Response<FileFormat>>(`/v1/files/${id}`, {
    method: "patch",
    data: { ...res },
  });
}
