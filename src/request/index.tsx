import axios from "axios";
import store from "store2";

/**
 * Types
 */
import type { AxiosError, AxiosResponse } from "axios";

/**
 * Axios
 */
axios.defaults.baseURL = import.meta.env.VITE_API_URL;

axios.defaults.timeout = 60 * 5 * 1000;

axios.interceptors.request.use((config) => {
  if (store.has("token")) {
    config.headers.Authorization = `Bearer ${store.get("token")}`;
  }

  return config;
});

axios.interceptors.response.use(
  (response: AxiosResponse) => {
    // 用户未登录
    // if (response.data.code === 11101) {
    //   message.error("用户未登录");
    //   store.remove("token");
    //   store.remove("user");
    //   location.href = "/login";
    // }
    return response;
  },
  (error: AxiosError) => {
    if (error.status == "401") {
      store.remove("user");
      store.remove("token");
      return (location.href = "/login");
    }

    return Promise.reject(error);
  }
);

export default axios;
