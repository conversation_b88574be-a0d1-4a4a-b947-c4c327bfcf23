import axios from "./index";
import type { Response } from "../vite-env";

export type Knowledge = {
  id?: string;
  name: string;
  description: string;
  createTime?: string;
};

export type KnowledgeList = {
  items: Knowledge[];
  total: number;
};

export function AKnowledge(data: Partial<Knowledge>) {
  return axios<Response<Knowledge>>(`/v1/knowledges`, {
    method: "post",
    data,
  });
}
export function UKnowledge(data: Partial<Knowledge>) {
  const { id, ...rest } = data;
  return axios<Response<Knowledge>>(`/v1/knowledges/${id}`, {
    method: "patch",
    data: {
      ...rest,
    },
  });
}
export function DKnowledge(id: string) {
    return axios<Response<Knowledge>>(`/v1/knowledges/${id}`, {
      method: "delete",
    });
}
export function Knowledges(params: {
  pageSize: number;
  pageNumber: number;
  name?: string;
}) {
  return axios<Response<KnowledgeList>>(`/v1/knowledges`, {
    method: "get",
    params,
  });
}
