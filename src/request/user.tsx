import axios from "./index";
import type { Response } from "../vite-env";

export type User = {
  id?: string;
  username: string;
  password: string;
  roles: string[];
  email: string;
  createTime?: string;
};
export type UserList = {
  items: User[];
  total: number;
};
export function AUser(data: User) {
  return axios<Response<User>>(`/v1/users`, {
    method: "post",
    data,
  });
}
export function Users(params?: { pageSize: number; pageNumber: number }) {
  return axios<Response<UserList>>(`/v1/users`, {
    method: "get",
    params,
  });
}
export function UUser(userId: string, data: Partial<User>) {
  return axios<Response<User>>(`/v1/users/${userId}`, {
    method: "patch",
    data,
  });
}
export function RUser(userId: string) {
  return axios<Response<User>>(`/v1/users/${userId}`, {
    method: "delete",
  });
}
export function ResetUser(data: Partial<User>) {
  return axios<Response<User>>(`/v1/user/reset`, {
    method: "post",
    data,
  });
}

export function ResetPwd(data: {
  id: string;
  oldPassword: string;
  password: string;
}) {
  const { id, ...res } = data;
  return axios<Response<User>>(`/v1/users/${id}`, {
    method: "patch",
    data: { ...res },
  });
}
