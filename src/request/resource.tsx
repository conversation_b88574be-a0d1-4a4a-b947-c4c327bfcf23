import axios from "./index";
import type { Response } from "../vite-env";
export type Resource = {
  id?: string;
  name: string;
  ip: string;
  port: number;
  maxSessions: number;
  maxProjects?: number;
  curProjects?: number;
  status: "Running" | "Stopped";
  userId?: string;
  createTime?: string;
  deleteTime?: string;
};

export type ResourceList = {
  items: Resource[];
  total: number;
};

export function AResource(data: Resource) {
  return axios<Response<Resource>>(`/v1/resources`, {
    method: "post",
    data,
  });
}

export function Resources(params?: { pageSize: number; pageNumber: number }) {
  return axios<Response<ResourceList>>(`/v1/resources`, {
    method: "get",
    params,
  });
}
export function RResource(resourceId: string) {
  return axios<Response<Resource>>(`/v1/resources/${resourceId}`, {
    method: "delete",
  });
}
export function UResource(resourceId: string, data: Partial<Resource>) {
  return axios<Response<Partial<Resource>>>(`/v1/resources/${resourceId}`, {
    method: "patch",
    data,
  });
}
