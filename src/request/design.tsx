import axios from "./index";
import type { Response } from "../vite-env";
export function AClip(formData: any) {
  return axios.post(`/v1/clips`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

export type Design = {
  id: string;
  backgroundUrl: string;
  settings: string;
  createTime: string;
  updateTime: string;
  projectId?: string;
};

export function RDesign(projectId: string) {
  return axios<Response<Design>>(`/v1/styles`, {
    method: "get",
    params: { projectId },
  });
}
export function CDesign(formData: any) {
  return axios.post(`/v1/styles`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
export function UDesign(formData: any, id: string, delBackground?: boolean) {
  return axios.patch(`/v1/styles/${id}`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
