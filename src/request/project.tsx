import axios from "./index";
import type { Response } from "../vite-env";
import { Clip } from "./clip";
import { Knowledge } from "./knowledge";

export type Project = {
  id?: string;
  name: string;
  videoClipId: string;
  audioClipId: string;
  type: string;
  publicUrl: string;
  knowledgeId: string;
  createTime?: string;
  audioClipName?: string;
  videoClipUrl?: string;
  status?: string;
};
export type ProjectList = {
  items: Project[];
  total: Number;
};

export function AProject(data: Partial<Project>) {
  return axios<Response<Project>>(`/v1/projects`, {
    method: "post",
    data,
  });
}

export function Projects(params: {
  pageSize?: number;
  pageNumber?: number;
  name?: string;
}) {
  return axios<Response<ProjectList>>(`/v1/projects`, {
    method: "get",
    params,
  });
}

export function UProject(data: Partial<Project>) {
  const { id: projectId, ...res } = data;
  return axios<Response<Project>>(`/v1/projects/${projectId}`, {
    method: "patch",
    data: { ...res },
  });
}

export function DProject(projectId: string) {
  return axios<Response<Project>>(`/v1/projects/${projectId}`, {
    method: "delete",
  });
}

export function AllVideo() {
  return axios<Response<Clip[]>>(`/v1/clips`, {
    method: "get",
    params: { type: "Video" },
  });
}
export function AllImage() {
  return axios<Response<Clip[]>>(`/v1/clips`, {
    method: "get",
    params: { type: "Image" },
  });
}

export function AllAudio() {
  return axios<Response<Clip[]>>(`/v1/clips`, {
    method: "get",
    params: { type: "Audio" },
  });
}
export function AllKnowledge() {
  return axios<Response<Knowledge[]>>(`/v1/knowledges`, {
    method: "get",
  });
}

export function RProject(projectId: string) {
  return axios<Response<Project>>(`/v1/projects/${projectId}`, {
    method: "get",
  });
}
