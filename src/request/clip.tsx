import axios from "./index";
import type { Response } from "../vite-env";

export type Clip = {
  id?: string;
  type: string;
  name: string;
  category: string;
  url: string;
  createTime?: string;
};
export type ClipList = {
  items: Clip[];
  total: number;
};

export function AClip(formData: any) {
  return axios.post(`/v1/clips`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
export function Clips(params: {
  type?: string;
  category?: string;
  pageSize?: number;
  pageNumber?: number;
}) {
  return axios<Response<ClipList>>(`/v1/clips`, {
    method: "get",
    params,
  });
}

export function DClip(clipId: string) {
  return axios<Response<Clip>>(`/v1/clips/${clipId}`, {
    method: "delete",
  });
}
