#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}
#root>.full-page-preview {
  display: flex;
  /* 或者其他布局方式 */
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  /* 垂直居中 */
  max-width: 1920px;
  margin: 0 auto;
  padding: 0;
}
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}



 /* 定义光影动画 */
 @keyframes shine {
   0% {
     background-position: 0 200%;
   }

   100% {
     background-position: 0 -200%;
   }
 }

 .button {
   position: absolute;
   pointer-events: auto;
   width: 50px;
   height: 50px;
   border-radius: 50%;
   background: radial-gradient(circle at center, #00A9B4 0%, #215FCC 100%);
   border: none;
   cursor: pointer;
   display: flex;
   align-items: center;
   justify-content: center;
   padding: 0;
   touch-action: none;
   opacity: 0.6;
   user-select: none;
   transition: transform 0.3s ease, opacity 0.3s ease, top 0.5s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.2s ease;
   box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
 }

 .button.clicked {
   transform: scale(1.2);
   opacity: 0.9;
   box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
   /* 阴影变小 */
 }

 /* 应用光影效果到文字 */
 #playBtn{
  position: fixed;
  top:50%;
  left: 50%;
  transform: translate(-50%,-50%);
  padding: 10px 20px;
  font-size:16px;
  background-color:#4C82E6;
  color: white;
  border:none;
  border-radius: 5px;
  cursor: pointer;
  z-index:1000;
 
 }
 #cancelContainer{
  margin-top: 70px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    color: #fff;
    font-size: 14px;
    opacity: 0;
 }
 #chat-container{
  /* position: absolute;
    width: 100%;
    height: 56%;
    top: 67%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 22;
    
    pointer-events: none;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    display: flex; */
 }
 #recordButtonContainer{
   position: absolute;
    right: 20px;
    top: 35%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
 }
 .shine-text {
   font-size: 14px;
   font-weight: bold;
   writing-mode: vertical-rl;
   background: linear-gradient(135deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 50%, rgba(255, 255, 255, 0) 100%);
   background-size: 100% 200%;
   -webkit-background-clip: text;
   background-clip: text;
   color: transparent;
   animation: shine 3s infinite linear;
 }

 #chat-container::-webkit-scrollbar {
   display: none;
 }

 #chat-messages::-webkit-scrollbar {
   display: none;
 }

 #chat-messages {
   /* display: flex;
   flex-direction: column;
   gap: 10px;
   pointer-events: auto;
   width: 100%;
   height: 100%;
   overflow-y: auto; */
   /* mask-image: linear-gradient(to top, black 70%, transparent);
                    -webkit-mask-image: linear-gradient(to top, black 70%, transparent); */
 }

 .user-message {
   background: linear-gradient(135deg, rgba(0, 169, 180, 0.5), rgba(0, 102, 255, 0.5)) !important;
   color: white !important;
   font-size: 18px !important;
   box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
 }

 .ai-message {
   background: rgba(53, 57, 63, 0.5) !important;
   color: white !important;
   font-size: 18px !important;
   box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05) !important;
 }

.messages {
  margin: 5px 0;
}
  
.messages:first-child {
   margin: 0;
}

.react-draggable {
  position: absolute;
}

.react-draggable-dragging {
  z-index: 1000;
}

.react-rnd-resizing {
  z-index: 1000;
}




