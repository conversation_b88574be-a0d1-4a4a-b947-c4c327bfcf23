import React, { createContext, useContext, useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import { useRequest } from "ahooks";
import { Login } from "@/request/login";
import type { Response } from "../vite-env";
import store from "store2";
import { jwtDecode } from "jwt-decode";

interface User {
  id: string;
  username: string;
  role: "admin" | "user";
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

// API base URL - replace with your actual API endpoint

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  const { toast } = useToast();

  useEffect(() => {
    // Check if user is already logged in
    const storedUser = store.get("user");

    if (storedUser) {
      try {
        setUser(JSON.parse(storedUser));
      } catch (error) {
        console.error("Failed to parse stored user:", error);
        store.remove("user");
      }
    }

    setIsLoading(false);
  }, []);

  const login = async (username: string, password: string) => {
    setIsLoading(true);

    try {
      const response = await Login({ userName: username, password });
      console.log("=====response", response);
      if (response.data.token && response.status === 200) {
        // You might need to adapt this based on your API response structure
        // const userData = response.data.user || {
        //   id: Date.now().toString(),
        //   username,
        //   role: username.toLowerCase() === 'admin' ? 'admin' : 'user'
        // };
        store.set("token", response.data.token);
        const result = jwtDecode(response.data.token);
        console.log("======result", result);
        if (result) {
          const userData = {
            id: result?.sub,
            username: result?.name,
            role: result?.roles?.[0],
          };
          setUser(userData);
          store.set("user", JSON.stringify(userData));
          toast({
            title: "登录成功",
            description: `欢迎回来, ${username}!`,
            variant: "default",
            duration: 5000,
          });
          navigate("/resources");
        } else {
          throw new Error("Token failed");
        }
      } else {
        throw new Error("Authentication failed");
      }
    } catch (error) {
      // store.remove('user')
      toast({
        title: "登录失败",
        description: "用户名或密码不正确,或网络错误",
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    store.remove("token");
    store.remove("user");
    setUser(null);
    navigate("/login");
    toast({
      title: "已退出登录",
      description: "您已成功退出登录",
      variant: "default",
      duration: 5000,
    });
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated: !!user && store.get("user"),
        isLoading,
        login,
        logout,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
