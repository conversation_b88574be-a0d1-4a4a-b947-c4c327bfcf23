:root {
  --bg-color: #ffffff;
}

.canvas-container {
  border: 1px solid #e0e0e0 !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  border-radius: 4px !important;
  padding: 0 !important;
  margin: 0 !important;
  display: inline-block !important;
  position: relative !important;
  overflow: visible !important;
}

.checkered-background {
  background-color: var(--bg-color) !important;
  background-image: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                    linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                    linear-gradient(-45deg, transparent 75%, #f0f0f0 75%) !important;
  background-size: 20px 20px !important;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: -1 !important;
  pointer-events: none !important;
}
