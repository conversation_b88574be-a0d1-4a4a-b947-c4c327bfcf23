import { useEffect } from "react";
import { Outlet, useNavigate, useLocation } from "react-router-dom";
import PageTransition from "@/components/ui/PageTransition";
import MainLayout from "@/components/layout/MainLayout";
import { useAuth } from "@/context/AuthContext";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AlertTriangle, Users, Server, Settings } from "lucide-react";

const SystemManagement = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const isAdmin = user?.role === "admin_user";

  useEffect(() => {
    // Redirect to users management by default
    if (location.pathname === "/system-management") {
      navigate("/system-management/users");
    }
  }, [location.pathname, navigate]);

  if (!isAdmin) {
    return (
      <MainLayout>
        <PageTransition>
          <div className="container max-w-7xl px-4 py-8">
            <Alert className="bg-destructive/10 border-destructive">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              <AlertDescription className="text-destructive">
                您没有访问系统管理页面的权限。此页面仅限管理员访问。
              </AlertDescription>
            </Alert>
          </div>
        </PageTransition>
      </MainLayout>
    );
  }

  const currentTab = location.pathname.includes("/users")
    ? "users"
    : "resources";

  const handleTabChange = (value: string) => {
    navigate(`/system-management/${value}`);
  };

  return (
    <MainLayout>
      <PageTransition>
        <div className="container max-w-7xl px-4 py-8">
          <div className="flex flex-col">
            <div className="flex items-center gap-2 mb-6">
              <Settings className="h-6 w-6 text-primary" />
              <h1 className="text-2xl font-semibold">系统管理</h1>
            </div>

            <Tabs
              value={currentTab}
              onValueChange={handleTabChange}
              className="mb-6"
            >
              <TabsList className="grid w-full md:w-[400px] grid-cols-2">
                <TabsTrigger value="users" className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  <span>用户管理</span>
                </TabsTrigger>
                <TabsTrigger
                  value="resources"
                  className="flex items-center gap-2"
                >
                  <Server className="h-4 w-4" />
                  <span>资源管理</span>
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Outlet will render the child routes */}
            <Outlet />
          </div>
        </div>
      </PageTransition>
    </MainLayout>
  );
};

export default SystemManagement;
