import { useState, useEffect } from "react";
import PageTransition from "@/components/ui/PageTransition";
import CustomMainLayout from "@/components/layout/CustomMainLayout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/components/ui/use-toast";
import axios from "axios";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Users,
  PlusCircle,
  Edit,
  Trash,
  UserPlus,
  ShieldAlert,
  IterationCcwIcon,
  CheckCircle,
  XCircle,
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import type { Response } from "../vite-env";
import { useRequest } from "ahooks";
import dayjs from "dayjs";

import {
  AUser,
  Users as AllUser,
  RUser,
  UUser,
  UserList,
  ResetUser,
  type User as UserFormat,
} from "@/request/user";

interface User {
  id: string;
  username: string;
  role: string;
  // status: "active" | "inactive";
  email: string;
}

const UserManagement = () => {
  const [users, setUsers] = useState<UserFormat[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isNewUserDialogOpen, setIsNewUserDialogOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(0);
  const [newUser, setNewUser] = useState({
    username: "",
    email: "",
    role: "normal_user",
    password: "",
    confirmPassword: "",
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const { user: currentUser } = useAuth();
  const { toast } = useToast();
  const [isResetDialogOpen, setIsResetDialogOpen] = useState(false);
  const [newPwd, setNewPwd] = useState("");
  const [rUserId, setRUserId] = useState("");

  // Calculate pagination values

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pageNumbers = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total pages are less than max visible
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // Always show first page
      pageNumbers.push(1);

      // Calculate start and end for visible pages
      let start = Math.max(2, currentPage - 1);
      let end = Math.min(totalPages - 1, currentPage + 1);

      // Adjust if we're at the beginning
      if (currentPage <= 2) {
        end = Math.min(totalPages - 1, maxVisiblePages - 1);
      }

      // Adjust if we're at the end
      if (currentPage >= totalPages - 2) {
        start = Math.max(2, totalPages - maxVisiblePages + 2);
      }

      // Add ellipsis after first page if needed
      if (start > 2) {
        pageNumbers.push("ellipsis");
      }

      // Add visible page numbers
      for (let i = start; i <= end; i++) {
        pageNumbers.push(i);
      }

      // Add ellipsis before last page if needed
      if (end < totalPages - 1) {
        pageNumbers.push("ellipsis");
      }

      // Always show last page
      pageNumbers.push(totalPages);
    }

    return pageNumbers;
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const { run: runAll, loading } = useRequest(AllUser, {
    manual: true,
    onSuccess: (data: Response<UserList>) => {
      if (data.data.items.length == 0 && currentPage > 1) {
        setCurrentPage(currentPage - 1);
      } else {
        setUsers([...data.data.items]);
        setTotalPages(Math.ceil(data.data.total / itemsPerPage));
      }
    },
    onError: () => {
      toast({
        title: "加载用户失败",
        description: "无法从服务器获取用户数据，使用演示数据代替",
        variant: "destructive",
      });
    },
  });

  useEffect(() => {
    runAll({ pageNumber: currentPage, pageSize: itemsPerPage });
  }, [currentPage]);

  const { run: runAdd } = useRequest(AUser, {
    manual: true,
    onSuccess: (data: Response<UserFormat>, params) => {
      if (currentPage != 1) {
        setCurrentPage(1);
      } else {
        runAll({ pageNumber: currentPage, pageSize: itemsPerPage });
      }

      toast({
        title: "用户已创建",
        description: `用户 ${newUser.username} 已成功创建`,
      });
    },

    onError(e, params) {
      toast({
        title: "保存用户失败",
        description: "无法保存到服务器，请稍后再试",
        variant: "destructive",
      });
    },
    onFinally(params, data, e) {
      setIsNewUserDialogOpen(false);
      setSelectedUser(null);
      setNewUser({
        username: "",
        email: "",
        role: "normal_user",
        password: "",
        confirmPassword: "",
      });
    },
  });
  const { run: runDelete } = useRequest(RUser, {
    manual: true,
    onSuccess: (data: Response<UserFormat>, params) => {
      toast({
        title: "用户已删除",
        description: "用户已成功从系统中删除",
      });
      runAll({ pageNumber: currentPage, pageSize: itemsPerPage });
    },
  });

  const { run: runUpdate } = useRequest(UUser, {
    manual: true,
    onSuccess: (data: Response<UserFormat>, params) => {
      runAll({ pageNumber: currentPage, pageSize: itemsPerPage });
      toast({
        title: "用户已保存",
        description: `用户 ${newUser.username} 已成功保存`,
      });
    },
    onError(e, params) {
      toast({
        title: "保存用户失败",
        description: "无法保存到服务器，请稍后再试",
        variant: "destructive",
      });
    },
    onFinally(params, data, e) {
      setIsNewUserDialogOpen(false);
      setSelectedUser(null);
      setNewUser({
        username: "",
        email: "",
        role: "normal_user",
        password: "",
        confirmPassword: "",
      });
    },
  });

  const { run: runReset } = useRequest(ResetUser, {
    manual: true,
    onSuccess: (data: Response<UserFormat>, params) => {
      toast({
        title: "密码已重置",
        description: `用户密码已修改成功`,
      });
    },
    onError(e, params) {
      toast({
        title: "密码修改失败",
        description: "无法保存到服务器，请稍后再试",
        variant: "destructive",
      });
    },
    onFinally(params, data, e) {
      setRUserId("");
      setNewPwd("");
      setIsResetDialogOpen(false);
    },
  });

  const handleStatusToggle = async (userId: string) => {
    try {
      const targetUser = users.find((u) => u.id === userId);
      if (!targetUser) return;

      const newStatus = targetUser.status === "active" ? "inactive" : "active";

      const response = await axios.patch(
        `${API_BASE_URL}/users/${userId}/status`,
        { status: newStatus },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        }
      );

      console.log("Toggle user status response:", response.data);

      if (response.data && response.data.success) {
        setUsers(
          users.map((user) =>
            user.id === userId ? { ...user, status: newStatus } : user
          )
        );
      } else {
        setUsers(
          users.map((user) =>
            user.id === userId ? { ...user, status: newStatus } : user
          )
        );
      }

      toast({
        title: "用户状态已更新",
        description: `用户 ${targetUser.username} 已${
          newStatus === "active" ? "启用" : "禁用"
        }`,
      });
    } catch (error) {
      console.error("Error toggling user status:", error);

      toast({
        title: "更新用户状态失败",
        description: "无法更新用户状态，请稍后再试",
        variant: "destructive",
      });

      setUsers(
        users.map((user) =>
          user.id === userId
            ? {
                ...user,
                status: user.status === "active" ? "inactive" : "active",
              }
            : user
        )
      );
    }
  };

  const handleEditUser = (user: UserFormat) => {
    setSelectedUser({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.roles?.[0],
    });
    setNewUser({
      username: user.username,
      email: user.email,
      role: user.roles?.[0],
      password: "",
      confirmPassword: "",
    });
    setIsNewUserDialogOpen(true);
  };

  const resetPwd = (user: UserFormat) => {
    setIsResetDialogOpen(true);
    setRUserId(user.id);
  };

  const handleDeleteUser = async (userId: string) => {
    runDelete(userId);
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!newUser.username.trim()) {
      newErrors.username = "用户名不能为空";
    }

    if (!newUser.email.trim()) {
      newErrors.email = "邮箱不能为空";
    } else if (!/\S+@\S+\.\S+/.test(newUser.email)) {
      newErrors.email = "邮箱格式不正确";
    }

    if (!selectedUser) {
      if (!newUser.password) {
        newErrors.password = "密码不能为空";
      } else if (newUser.password.length < 6) {
        newErrors.password = "密码长度不能少于6个字符";
      }

      if (newUser.password !== newUser.confirmPassword) {
        newErrors.confirmPassword = "两次输入的密码不一致";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSaveUser = async () => {
    if (!validateForm()) return;

    if (selectedUser) {
      runUpdate(selectedUser.id, {
        username: newUser.username,
        email: newUser.email,
        roles: [newUser.role],
      });
    } else {
      runAdd({
        username: newUser.username,
        email: newUser.email,
        roles: [newUser.role],
        password: newUser.password,
      });
    }
  };

  const resetForm = () => {
    setSelectedUser(null);
    setNewUser({
      username: "",
      email: "",
      role: "normal_user",
      password: "",
      confirmPassword: "",
    });
    setErrors({});
  };

  const addNewUser = () => {
    resetForm();
    setIsNewUserDialogOpen(true);
  };
  const resetPassword = () => {
    runReset({
      id: rUserId,
      password: newPwd,
    });
  };

  return (
    <CustomMainLayout>
      <PageTransition>
        <div className="container max-w-7xl px-4 py-8">
          <div className="flex flex-col">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-2">
                <Users className="h-6 w-6 text-primary" />
                <h1 className="text-2xl font-semibold">用户管理</h1>
              </div>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button onClick={addNewUser}>
                      <UserPlus className="mr-2 h-4 w-4" />
                      添加用户
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>创建新用户账号</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>用户列表</CardTitle>
                <CardDescription>管理系统用户账号和权限</CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex justify-center p-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : (
                  <>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="text-center">用户名</TableHead>
                          <TableHead className="text-center">邮箱</TableHead>
                          <TableHead className="text-center">角色</TableHead>
                          {/* <TableHead>状态</TableHead> */}
                          <TableHead className="text-center">
                            创建时间
                          </TableHead>
                          <TableHead className="text-center">操作</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {users.map((user) => (
                          <TableRow key={user.id}>
                            <TableCell className="font-medium">
                              {user.username}
                            </TableCell>
                            <TableCell>{user.email}</TableCell>
                            <TableCell>
                              {user.roles[0] === "admin_user" ? (
                                <div className="flex items-center justify-center gap-1 text-primary">
                                  <ShieldAlert className="h-4 w-4" />
                                  <span>管理员</span>
                                </div>
                              ) : (
                                <div className="flex items-center justify-center gap-1">
                                  <Users className="h-4 w-4" />
                                  <span>普通用户</span>
                                </div>
                              )}
                            </TableCell>
                            {/* <TableCell>
                            <div className="flex items-center gap-2">
                              {user.status === "active" ? (
                                <>
                                  <CheckCircle className="h-4 w-4 text-green-500" />{" "}
                                  活跃
                                </>
                              ) : (
                                <>
                                  <XCircle className="h-4 w-4 text-muted-foreground" />{" "}
                                  已禁用
                                </>
                              )}
                            </div>
                          </TableCell> */}
                            <TableCell>
                              {user.createTime
                                ? dayjs(user.createTime).format(
                                    "YYYY-MM-DD HH:mm:ss"
                                  )
                                : ""}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-center gap-2">
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                          variant="outline"
                                          size="icon"
                                          onClick={() => handleEditUser(user)}
                                          disabled={currentUser?.id === user.id}
                                      >
                                        <Edit className="h-4 w-4" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>编辑用户</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                          variant="outline"
                                          size="icon"
                                          onClick={() => resetPwd(user)}
                                          disabled={currentUser?.id === user.id}
                                      >
                                        <IterationCcwIcon className="h-4 w-4" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>重置密码</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <TooltipProvider>
                                      <Tooltip>
                                        <TooltipTrigger asChild>
                                          <Button
                                              variant="outline"
                                              size="icon"
                                              disabled={currentUser?.id === user.id}
                                          >
                                            <Trash className="h-4 w-4 text-destructive" />
                                          </Button>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                          <p>删除用户</p>
                                        </TooltipContent>
                                      </Tooltip>
                                    </TooltipProvider>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>
                                        确认删除
                                      </AlertDialogTitle>
                                      <AlertDialogDescription>
                                        您确定要删除用户 {user.username}{" "}
                                        吗？此操作不可逆。
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel>
                                        取消
                                      </AlertDialogCancel>
                                      <AlertDialogAction
                                        onClick={() =>
                                          handleDeleteUser(user.id)
                                        }
                                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                      >
                                        删除
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                    {totalPages > 1 && (
                      <div className="mt-4 flex justify-center">
                        <Pagination>
                          <PaginationContent>
                            <PaginationItem>
                              <PaginationPrevious
                                onClick={() =>
                                  handlePageChange(Math.max(1, currentPage - 1))
                                }
                                className={
                                  currentPage === 1
                                    ? "pointer-events-none opacity-50"
                                    : "cursor-pointer"
                                }
                              />
                            </PaginationItem>

                            {getPageNumbers().map((number, index) => (
                              <PaginationItem key={index}>
                                {number === "ellipsis" ? (
                                  <PaginationEllipsis />
                                ) : (
                                  <PaginationLink
                                    isActive={currentPage === number}
                                    onClick={() =>
                                      handlePageChange(number as number)
                                    }
                                    className="cursor-pointer"
                                  >
                                    {number}
                                  </PaginationLink>
                                )}
                              </PaginationItem>
                            ))}

                            <PaginationItem>
                              <PaginationNext
                                onClick={() =>
                                  handlePageChange(
                                    Math.min(totalPages, currentPage + 1)
                                  )
                                }
                                className={
                                  currentPage === totalPages
                                    ? "pointer-events-none opacity-50"
                                    : "cursor-pointer"
                                }
                              />
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      </div>
                    )}
                  </>
                )}
              </CardContent>
            </Card>
          </div>

          <Dialog
            open={isResetDialogOpen}
            onOpenChange={(open) => {
              if (!open) setNewPwd("");
              console.log("=======tests");
              setIsResetDialogOpen(open);
            }}
          >
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>重置密码</DialogTitle>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="username" className="text-right">
                    新密码
                  </Label>
                  <div className="col-span-3 space-y-1">
                    <Input
                      value={newPwd}
                      onChange={(e) => setNewPwd(e.target.value)}
                    />
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => {
                    setNewPwd("");
                    setIsResetDialogOpen(false);
                  }}
                >
                  取消
                </Button>
                <Button disabled={!newPwd} onClick={resetPassword}>
                  重置
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Dialog
            open={isNewUserDialogOpen}
            onOpenChange={(open) => {
              if (!open) resetForm();
              setIsNewUserDialogOpen(open);
            }}
          >
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>
                  {selectedUser ? "编辑用户" : "创建新用户"}
                </DialogTitle>
                <DialogDescription>
                  {selectedUser
                    ? "修改用户信息和权限"
                    : "填写以下信息创建新用户账号"}
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="username" className="text-right">
                    用户名
                  </Label>
                  <div className="col-span-3 space-y-1">
                    <Input
                      id="username"
                      value={newUser.username}
                      onChange={(e) =>
                        setNewUser({ ...newUser, username: e.target.value })
                      }
                      className={errors.username ? "border-destructive" : ""}
                    />
                    {errors.username && (
                      <p className="text-sm text-destructive">
                        {errors.username}
                      </p>
                    )}
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="email" className="text-right">
                    邮箱
                  </Label>
                  <div className="col-span-3 space-y-1">
                    <Input
                      id="email"
                      type="email"
                      value={newUser.email}
                      onChange={(e) =>
                        setNewUser({ ...newUser, email: e.target.value })
                      }
                      className={errors.email ? "border-destructive" : ""}
                    />
                    {errors.email && (
                      <p className="text-sm text-destructive">{errors.email}</p>
                    )}
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="role" className="text-right">
                    角色
                  </Label>
                  <div className="col-span-3">
                    <RadioGroup
                      value={newUser.role}
                      onValueChange={(value) =>
                        setNewUser({
                          ...newUser,
                          role: value,
                        })
                      }
                      className="flex gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="normal_user" id="user-role" />
                        <Label
                          htmlFor="user-role"
                          className="cursor-pointer flex items-center gap-1"
                        >
                          <Users className="h-4 w-4" />
                          普通用户
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="admin_user" id="admin-role" />
                        <Label
                          htmlFor="admin-role"
                          className="cursor-pointer flex items-center gap-1"
                        >
                          <ShieldAlert className="h-4 w-4" />
                          管理员
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>
                </div>

                {!selectedUser && (
                  <>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="password" className="text-right">
                        密码
                      </Label>
                      <div className="col-span-3 space-y-1">
                        <Input
                          id="password"
                          type="password"
                          value={newUser.password}
                          onChange={(e) =>
                            setNewUser({ ...newUser, password: e.target.value })
                          }
                          className={
                            errors.password ? "border-destructive" : ""
                          }
                        />
                        {errors.password && (
                          <p className="text-sm text-destructive">
                            {errors.password}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="confirmPassword" className="text-right">
                        确认密码
                      </Label>
                      <div className="col-span-3 space-y-1">
                        <Input
                          id="confirmPassword"
                          type="password"
                          value={newUser.confirmPassword}
                          onChange={(e) =>
                            setNewUser({
                              ...newUser,
                              confirmPassword: e.target.value,
                            })
                          }
                          className={
                            errors.confirmPassword ? "border-destructive" : ""
                          }
                        />
                        {errors.confirmPassword && (
                          <p className="text-sm text-destructive">
                            {errors.confirmPassword}
                          </p>
                        )}
                      </div>
                    </div>
                  </>
                )}
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsNewUserDialogOpen(false)}
                >
                  取消
                </Button>
                <Button onClick={handleSaveUser}>
                  {selectedUser ? "保存更改" : "创建用户"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </PageTransition>
    </CustomMainLayout>
  );
};

export default UserManagement;
