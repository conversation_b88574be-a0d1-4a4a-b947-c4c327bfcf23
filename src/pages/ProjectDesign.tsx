import { useState, useEffect, useRef } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Slider } from "@/components/ui/slider";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import PageTransition from "@/components/ui/PageTransition";
import MainLayout from "@/components/layout/MainLayout";
import LoadingDialog from "@/components/ui/LoadingDialog";
import {
  Mic,
  ArrowLeft,
  Save,
  Image,
  Type,
  PaintBucket,
  Trash2,
  MoveHorizontal,
  MoveVertical,
  MessageSquare,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Rnd } from "react-rnd";

import store from "store2";
import { RDesign, UDesign, type Design, CDesign } from "@/request/design";
import { useRequest } from "ahooks";
import type { Response } from "../../vite-env";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { hexToRGBA } from "@/lib/utils";

interface Element {
  id: string;
  type: "text" | "video" | "button" | "subtitle" | "chat";
  x: number;
  y: number;
  width?: number;
  height?: number;
  content?: string;
  fontSize?: number;
  color?: string;
  fontWeight?: string;
  backgroundColor?: string;
  size?: number;
  iconScale?: number;
  // 字幕框特有属性
  padding?: number;
  fontPadding?: number;
  borderRadius?: number;
  opacity?: number;
  fontFamily?: string;
  btnOpacity?: number;
}

interface ProjectDesign {
  id: string;
  projectId: string;
  backgroundColor: string;
  backgroundImage: string | null;
  elements: Element[];
  aspectRatio: string;
  subtitles: boolean;
  opacity?: number;
  version: number;
}

interface Project {
  id: string;
  title: string;
  avatar: {
    id: string;
    type: "video" | "image";
    thumbnail: string;
  };
}

const defaultDesign: ProjectDesign = {
  id: "",
  projectId: "",
  backgroundColor: "#ffffff",
  backgroundImage: null,
  opacity: 1,
  elements: [
    // Default video element
    {
      id: "video-element",
      type: "video",
      x: 38,
      y: 7,
      width: 28,
      height: 0, // aspect ratio controlled
    },
    // Default mic button
    {
      id: "chat-button",
      type: "chat",
      x: 45,
      y: 84,
      size: 40,
      backgroundColor: "#cccccc",
      color: "#000000",
      iconScale: 0.5,
      btnOpacity: 1,
    },
    {
      id: "mic-button",
      type: "button",
      x: 53,
      y: 84,
      size: 40,
      backgroundColor: "#cccccc",
      color: "#000000",
      iconScale: 0.5,
      btnOpacity: 1,
    },
    {
      id: "subtitle-container",
      type: "subtitle",
      x: 39,
      y: 26,
      width: 25, // 百分比
      height: 50, // 百分比
      backgroundColor: "#357eb6",
      opacity: 0.5,
      padding: 16,
      borderRadius: 8,
      fontSize: 16,
      color: "#FFFFFF",
      fontFamily: "system-ui",
      fontWeight: "normal",
      fontPadding: 10,
    },
  ],
  aspectRatio: "16:9",
  subtitles: true,
  version: 1,
};

const defaultTextElement: Element = {
  id: crypto.randomUUID(),
  type: "text",
  content: "添加文本",
  x: 50,
  y: 90,
  fontSize: 16,
  color: "#000000",
  fontWeight: "normal",
};

const ProjectDesign = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [project, setProject] = useState<Project | null>(null);
  const [design, setDesign] = useState<ProjectDesign>({
    ...defaultDesign,
    projectId: projectId || "",
  });
  const [activeElementIndex, setActiveElementIndex] = useState<number | null>(
    null
  );

  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [file, setFile] = useState(null);
  const [preImg, setPreImg] = useState("");
  const designContainerRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [lastResize, setLastResize] = useState(Date.now());

  const { run } = useRequest(RDesign, {
    defaultParams: [projectId],
    onSuccess: (data: Response<Design>) => {
      if (data?.data?.settings) {
        const result = data.data.settings;
        if (data?.data?.backgroundUrl) {
          setPreImg(data?.data?.backgroundUrl);
        }
        try {
          const designJson = JSON.parse(result);

          if (designJson.elements)
            setDesign({
              ...designJson,
              projectId,
              id: data.data.id,
              backgroundImage: data?.data?.backgroundUrl,
            });
        } catch (e) {
          console.error("设计数据转化失败", e);
        }
      }
    },
    onError: (e) => {
      console.error(e);
    },
  });
  const { run: runUpdate } = useRequest(UDesign, {
    manual: true,
    onSuccess: (data: Response<Design>) => {
      toast({
        title: "保存成功",
        description: "页面设计已保存",
      });
      setTimeout(() => {
        navigate(`/project/preview/${projectId}`);
      }, 1500);
    },
    onError: () => {
      toast({
        title: "保存失败",
        description: "无法更新获取项目设计数据",
        variant: "destructive",
      });
    },
    onFinally: () => {
      setIsLoading(false);
    },
  });

  const { run: runCreate } = useRequest(CDesign, {
    manual: true,
    onSuccess: (data: Response<Design>) => {
      toast({
        title: "保存成功",
        description: "页面设计已保存",
      });
      setTimeout(() => {
        navigate(`/project/preview/${projectId}`);
      }, 1500);
    },
    onError: () => {
      toast({
        title: "保存失败",
        description: "无法更新获取项目设计数据",
        variant: "destructive",
      });
    },
    onFinally: () => {
      setIsLoading(false);
    },
  });

  const handleAspectRatioChange = (value: string) => {
    setDesign((pre) => {
      return {
        ...pre,
        aspectRatio: value,
      };
    });
  };
  const changeSubtitles = (value: boolean) => {
    setDesign((pre) => {
      return { ...pre, subtitles: value };
    });
  };

  useEffect(() => {
    if (design.aspectRatio) {
      if (design.aspectRatio === "16:9") {
        setDesign((pre) => {
          return {
            ...pre,
            elements: pre.elements.map((item, index) => {
              if (index == 0) {
                return { ...item, width: 28, x: 38, y: 7 };
              }
              if (index == 1) {
                return { ...item, x: 45, y: 84 };
              }
              if (index == 2) {
                return { ...item, x: 53, y: 84 };
              }
              if (index == 3) {
                return { ...item, x: 39, y: 26, width: 26, height: 53 };
              }
              return item;
            }),
          };
        });
      } else if (design.aspectRatio === "9:16") {
        setDesign((pre) => {
          return {
            ...pre,
            elements: pre.elements.map((item, index) => {
              if (index == 0) {
                return { ...item, width: 90, x: 6, y: 5 };
              }
              if (index == 1) {
                return { ...item, x: 25, y: 86 };
              }
              if (index == 2) {
                return { ...item, x: 58, y: 86 };
              }
              if (index == 3) {
                return { ...item, x: 9, y: 25, width: 84, height: 53 };
              }
              return item;
            }),
          };
        });
      } else if (design.aspectRatio === "4:3") {
        setDesign((pre) => {
          return {
            ...pre,
            elements: pre.elements.map((item, index) => {
              if (index == 0) {
                return { ...item, width: 38, x: 28, y: 6 };
              }
              if (index == 1) {
                return { ...item, x: 37, y: 86 };
              }
              if (index == 2) {
                return { ...item, x: 48, y: 86 };
              }
              if (index == 3) {
                return { ...item, width: 35, height: 55, x: 30, y: 22 };
              }
              return item;
            }),
          };
        });
      } else if (design.aspectRatio === "3:4") {
        setDesign((pre) => {
          return {
            ...pre,
            elements: pre.elements.map((item, index) => {
              if (index == 0) {
                return { ...item, width: 66, x: 17, y: 6 };
              }
              if (index == 1) {
                return { ...item, x: 33, y: 85 };
              }
              if (index == 2) {
                return { ...item, x: 55, y: 85 };
              }
              if (index == 3) {
                return { ...item, width: 60, height: 55, x: 20, y: 22 };
              }
              return item;
            }),
          };
        });
      }
      // setDesign((pre) => {
      //   return {
      //     ...pre,
      //     elements: [defaultDesign.elements[0], ...pre.elements.slice(1)],
      //   };
      // });
    }
  }, [design.aspectRatio]);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const formData = new FormData();
      formData.append("projectId", projectId);
      if (file) {
        formData.append("file", file);
        const temp = { ...design, backgroundImage: "" };
        formData.append("settings", JSON.stringify({ ...temp }));
      } else {
        formData.append("settings", JSON.stringify({ ...design }));
        if (design.id && preImg) {
          formData.append("delBackground", "true");
        }
      }
      if (design.id) {
        runUpdate(formData, design.id);
      } else {
        runCreate(formData);
      }
    } catch (error) {
      console.error("Error saving design:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleBackgroundColorChange = (color: string) => {
    setDesign({ ...design, backgroundColor: color });
  };

  const handleBackgroundImageUpload = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      const result = event.target?.result as string;
      setFile(file);
      setDesign({ ...design, backgroundImage: result });
    };
    reader.readAsDataURL(file);
  };

  const handleAddText = () => {
    const newTextElement = {
      ...defaultTextElement,
      id: crypto.randomUUID(),
    };
    setDesign({
      ...design,
      elements: [...design.elements, newTextElement],
    });
    setActiveElementIndex(design.elements.length);
  };

  const handleElementChange = (index: number, field: string, value: any) => {
    const updatedElements = [...design.elements];
    updatedElements[index] = { ...updatedElements[index], [field]: value };
    setDesign({ ...design, elements: updatedElements });
  };

  const handleBackgroundOpacityChange = (value: any) => {
    setDesign({ ...design, opacity: value });
  };

  const handleRemoveElement = (index: number) => {
    // Don't allow removing video or button elements (only text)
    const elementType = design.elements[index].type;
    if (elementType === "video" || elementType === "button") {
      toast({
        title: "无法删除",
        description: `${
          elementType === "video" ? "视频" : "按钮"
        }元素是必需的，不能删除`,
        variant: "destructive",
      });
      return;
    }

    const updatedElements = design.elements.filter((_, i) => i !== index);
    setDesign({ ...design, elements: updatedElements });
    setActiveElementIndex(null);
  };

  const handleElementDrag = (e: React.MouseEvent, index: number) => {
    if (!designContainerRef.current) return;

    const startX = e.clientX;
    const startY = e.clientY;
    const element = design.elements[index];
    const startElemX = element.x;
    const startElemY = element.y;

    const handleMouseMove = (moveEvent: MouseEvent) => {
      const containerRect = designContainerRef.current?.getBoundingClientRect();
      if (!containerRect) return;

      const dx = moveEvent.clientX - startX;
      const dy = moveEvent.clientY - startY;

      // Calculate percentage positions
      const containerWidth = containerRect.width;
      const containerHeight = containerRect.height;

      let newX = startElemX + (dx / containerWidth) * 100;
      let newY = startElemY + (dy / containerHeight) * 100;

      // Clamp within container
      newX = Math.max(0, Math.min(100, newX));
      newY = Math.max(0, Math.min(100, newY));

      const updatedElements = [...design.elements];
      updatedElements[index] = { ...updatedElements[index], x: newX, y: newY };
      console.log("JSON data", { ...design, elements: updatedElements });
      setDesign({ ...design, elements: updatedElements });
    };

    const handleMouseUp = () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };

    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
  };

  const handleElementResize = (index: number, newWidth: number) => {
    const element = design.elements[index];
    if (element.type !== "video") return;

    const updatedElements = [...design.elements];
    updatedElements[index] = { ...updatedElements[index], width: newWidth };
    setDesign({ ...design, elements: updatedElements });
  };
  const removeImg = () => {
    setFile(null);
    setPreImg("");
    setDesign({ ...design, backgroundImage: null });
  };

  // Find the indices of the video and mic button elements
  const videoElementIndex = design.elements.findIndex(
    (el) => el.type === "video"
  );
  const micButtonElementIndex = design.elements.findIndex(
    (el) => el.type === "button"
  );
  const chatButtonElementIndex = design.elements.findIndex(
    (el) => el.type === "chat"
  );

  // 添加字幕框的控制面板
  const subtitleElementIndex = design.elements.findIndex(
    (el) => el.type === "subtitle"
  );
  function dealSize(element: any) {
    const size: { width: number; height: number } = { width: 100, height: 100 };
    if (
      element.type === "video" ||
      element.type === "subtitle" ||
      element.type == "text"
    ) {
      size.width =
        (element.width / 100) * designContainerRef.current?.clientWidth || 100;
      size.height =
        (element.height / 100) * designContainerRef.current?.clientHeight ||
        100;
    } else {
      size.width = element.size;

      size.height = element.size;
      console.log("size", size);
    }

    return size;
  }
  useEffect(() => {
    if (!designContainerRef.current) return;

    // 创建 ResizeObserver 监听器
    const observer = new ResizeObserver((entries) => {
      // 父元素尺寸变化时更新状态
      setLastResize(Date.now());
    });

    // 监听父元素
    observer.observe(designContainerRef.current);

    // 清除监听
    return () => observer.disconnect();
  }, []);

  return (
    <MainLayout>
      <PageTransition>
        <div className="container max-w-7xl px-4 py-4">
          <div className="flex justify-between items-center mb-6">
            <Button
              variant="outline"
              size="sm"
              className="gap-2"
              onClick={() =>
                setDesign((pre) => {
                  (defaultDesign.id = pre.id || ""),
                    (defaultDesign.projectId = projectId || "");
                  return { ...defaultDesign };
                })
              }
            >
              重置
            </Button>

            <div className="flex items-center gap-3">
              <h1 className="text-2xl font-semibold">
                {project?.title || "页面设计"}
              </h1>
              <Button onClick={handleSave} className="gap-2">
                <Save className="h-4 w-4" />
                保存设计
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <div className="flex items-center gap-6 mb-4">
                {/* 增加 gap-6 使两个控件间距更大 */}
                <div className="flex items-center gap-2">
                  <Label>画布比例</Label>
                  <Select
                    value={design.aspectRatio}
                    onValueChange={handleAspectRatioChange}
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="选择比例" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="16:9">16:9</SelectItem>
                      <SelectItem value="9:16">9:16</SelectItem>
                      <SelectItem value="4:3">4:3</SelectItem>
                      <SelectItem value="3:4">3:4</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center gap-2">
                  <Label htmlFor="show-subtitles">显示字幕</Label>
                  <Switch
                    id="show-subtitles"
                    checked={design.subtitles}
                    onCheckedChange={changeSubtitles}
                  />
                </div>
              </div>
              <div
                ref={designContainerRef}
                className="h-[450px] mx-auto rounded-lg overflow-hidden shadow-lg relative border"
                style={{
                  aspectRatio: design.aspectRatio.replace(":", "/"),
                  backgroundColor: hexToRGBA(
                    design.backgroundColor,
                    design.opacity || 1
                  ),
                  backgroundImage: design.backgroundImage
                    ? `url(${design.backgroundImage})`
                    : "none",

                  backgroundSize: "cover",
                  backgroundPosition: "center",
                }}
              >
                {/* Elements */}
                {design.elements.map((element, index) => {
                  // 计算初始尺寸和位置
                  const position = {
                    x:
                      (element.x / 100) *
                        designContainerRef.current?.clientWidth || 0,
                    y:
                      (element.y / 100) *
                        designContainerRef.current?.clientHeight || 0,
                  };

                  const size = dealSize(element);

                  return (
                    <>
                      {(design.subtitles ||
                        (!design.subtitles && element.type !== "subtitle")) && (
                        <Rnd
                          key={lastResize + element.id}
                          default={{
                            x: position.x,
                            y: position.y,
                            width: size.width,
                            height: size.height,
                          }}
                          position={{
                            // 添加 position 属性
                            x: position.x,
                            y: position.y,
                          }}
                          size={{
                            // 添加 size 属性
                            width: size.width,
                            height: size.height,
                          }}
                          bounds="parent"
                          onDragStop={(e, d) => {
                            // 转换为百分比位置
                            const containerWidth =
                              designContainerRef.current?.clientWidth || 1;
                            const containerHeight =
                              designContainerRef.current?.clientHeight || 1;

                            const newX = (d.x / containerWidth) * 100;
                            const newY = (d.y / containerHeight) * 100;

                            const updatedElements = [...design.elements];
                            updatedElements[index] = {
                              ...updatedElements[index],
                              x: newX,
                              y: newY,
                            };
                            setDesign({ ...design, elements: updatedElements });
                          }}
                          onResizeStop={(
                            e,
                            direction,
                            ref,
                            delta,
                            position
                          ) => {
                            // 转换为百分比尺寸
                            const containerWidth =
                              designContainerRef.current?.clientWidth || 1;

                            const newWidth = parseInt(
                              //@ts-ignore
                              (ref.clientWidth / containerWidth) * 100
                            );
                            const containerHeight =
                              designContainerRef.current?.clientHeight || 1;
                            const newHeight = parseInt(
                              //@ts-ignore
                              (ref.clientHeight / containerHeight) * 100
                            );

                            const updatedElements = [...design.elements];
                            if (
                              updatedElements[index].type == "button" ||
                              updatedElements[index].type == "chat"
                            ) {
                              updatedElements[index] = {
                                ...updatedElements[index],
                                //@ts-ignore
                                size: parseInt(ref.clientWidth),
                                x: (position.x / containerWidth) * 100,
                                y: (position.y / containerHeight) * 100,
                              };
                            } else {
                              updatedElements[index] = {
                                ...updatedElements[index],
                                width: newWidth,
                                height: newHeight,
                                x: (position.x / containerWidth) * 100,
                                y: (position.y / containerHeight) * 100,
                              };
                            }
                            setDesign({ ...design, elements: updatedElements });
                          }}
                          onClick={() => setActiveElementIndex(index)}
                        >
                          {element.type === "video" && (
                            <div
                              className={cn(
                                "cursor-move",
                                activeElementIndex === index
                                  ? "ring-2 ring-primary ring-offset-2"
                                  : ""
                              )}
                            >
                              <AspectRatio
                                ratio={9 / 16}
                                className="w-full"
                                style={{
                                  backgroundColor: hexToRGBA("#cccccc", 0.5),
                                }}
                              >
                                <video
                                  className="w-full h-full object-cover"
                                  poster={project?.avatar.thumbnail}
                                  controls={false}
                                  loop
                                  muted
                                  playsInline
                                  src="https://lovable-code-demo.netlify.app/sample-video.mp4"
                                ></video>
                              </AspectRatio>
                            </div>
                          )}

                          {element.type === "text" && (
                            <div
                              key={lastResize + element.id}
                              className={cn(
                                "cursor-move select-none",
                                activeElementIndex === index
                                  ? "ring-2 ring-primary ring-offset-2"
                                  : ""
                              )}
                              style={{
                                left: `${element.x}%`,
                                top: `${element.y}%`,
                                fontSize: `${element.fontSize}px`,
                                color: element.color,
                                fontWeight: element.fontWeight,
                              }}
                              onClick={() => setActiveElementIndex(index)}
                              onMouseDown={(e) => handleElementDrag(e, index)}
                            >
                              {element.content}
                            </div>
                          )}

                          {element.type === "subtitle" && design.subtitles && (
                            <div
                              key={lastResize + element.id}
                              className={cn(
                                "cursor-move",
                                activeElementIndex === index
                                  ? "ring-2 ring-primary ring-offset-2"
                                  : ""
                              )}
                              style={{
                                width: "100%",
                                height: "100%",
                                overflow: "hidden",
                                borderRadius: `${element.borderRadius}px`,

                                padding: `${element.padding}px`,

                                fontSize: `${element.fontSize}px`,
                                color: element.color,
                                fontFamily: element.fontFamily,
                                fontWeight: element.fontWeight,
                              }}
                            >
                              <p
                                style={{
                                  backgroundColor: hexToRGBA(
                                    element.backgroundColor,
                                    element.opacity || 1
                                  ),
                                  wordBreak: "break-all",
                                  textAlign: "left",
                                  padding: `${element.fontPadding}px`,
                                  borderRadius: `${element.borderRadius}px`,
                                }}
                              >
                                示例字幕文本
                              </p>
                            </div>
                          )}

                          {element.type == "button" && (
                            <div
                              key={element.id}
                              className={cn(
                                "cursor-move",
                                activeElementIndex === index
                                  ? "ring-2 ring-primary ring-offset-2"
                                  : ""
                              )}
                              style={{
                                width: "100%",
                                height: "100%",
                                overflow: "hidden",
                              }}
                            >
                              <div
                                className="flex flex-col items-center"
                                style={{ width: "100%", height: "100%" }}
                              >
                                <Button
                                  className={`rounded-full flex items-center justify-center`}
                                  style={{
                                    backgroundColor: hexToRGBA(
                                      element.backgroundColor,
                                      element.btnOpacity || 1
                                    ),
                                    width: `100%`,
                                    height: `100%`,
                                  }}
                                >
                                  <Mic
                                    style={{
                                      width: `${
                                        element.size * element.iconScale
                                      }px`,
                                      height: `${
                                        element.size * element.iconScale
                                      }px`,
                                    }}
                                    color={element.color}
                                  />
                                </Button>
                              </div>
                            </div>
                          )}
                          {element.type == "chat" && (
                            <div
                              key={element.id}
                              className={cn(
                                "cursor-move",
                                activeElementIndex === index
                                  ? "ring-2 ring-primary ring-offset-2"
                                  : ""
                              )}
                              style={{ width: "100%", height: "100%" }}
                            >
                              <div
                                className="flex flex-col items-center"
                                style={{ width: "100%", height: "100%" }}
                              >
                                <Button
                                  className="rounded-full flex items-center justify-center record-button"
                                  style={{
                                    backgroundColor: hexToRGBA(
                                      element.backgroundColor,
                                      element.btnOpacity || 1
                                    ),
                                    width: "100%",
                                    height: "100%",
                                  }}
                                >
                                  <MessageSquare
                                    style={{
                                      width: `${
                                        element.size * element.iconScale
                                      }px`,
                                      height: `${
                                        element.size * element.iconScale
                                      }px`,
                                    }}
                                    color={element.color}
                                  />
                                </Button>
                              </div>
                            </div>
                          )}
                        </Rnd>
                      )}
                    </>
                  );
                  // Render different element types
                  if (element.type === "video") {
                    return (
                      <div
                        key={element.id}
                        className={cn(
                          "absolute cursor-move",
                          activeElementIndex === index
                            ? "ring-2 ring-primary ring-offset-2"
                            : ""
                        )}
                        style={{
                          left: `${element.x}%`,
                          top: `${element.y}%`,
                          width: `${element.width}%`,
                          transform: "translate(-50%, -50%)",
                        }}
                        onClick={() => setActiveElementIndex(index)}
                        onMouseDown={(e) => handleElementDrag(e, index)}
                      >
                        <div className="overflow-hidden">
                          <AspectRatio ratio={9 / 16} className="w-full">
                            <video
                              className="w-full h-full object-cover"
                              poster={project?.avatar.thumbnail}
                              controls={false}
                              loop
                              muted
                              playsInline
                              src="https://lovable-code-demo.netlify.app/sample-video.mp4"
                            ></video>
                          </AspectRatio>
                        </div>
                      </div>
                    );
                  } else if (element.type === "button") {
                    return (
                      <div
                        key={element.id}
                        className={cn(
                          "absolute cursor-move",
                          activeElementIndex === index
                            ? "ring-2 ring-primary ring-offset-2"
                            : ""
                        )}
                        style={{
                          left: `${element.x}%`,
                          top: `${element.y}%`,
                          transform: "translate(-50%, -50%)",
                        }}
                        onClick={() => setActiveElementIndex(index)}
                        onMouseDown={(e) => handleElementDrag(e, index)}
                      >
                        <div className="flex flex-col items-center">
                          <Button
                            className={`rounded-full flex items-center justify-center`}
                            style={{
                              backgroundColor: hexToRGBA(
                                element.backgroundColor,
                                element.btnOpacity || 1
                              ),
                              width: `${element.size}px`,
                              height: `${element.size}px`,
                            }}
                          >
                            <Mic
                              style={{
                                width: `${element.size * element.iconScale}px`,
                                height: `${element.size * element.iconScale}px`,
                              }}
                              color={element.color}
                            />
                          </Button>
                        </div>
                      </div>
                    );
                  } else if (element.type === "text") {
                    return (
                      <div
                        key={element.id}
                        className={cn(
                          "absolute cursor-move select-none",
                          activeElementIndex === index
                            ? "ring-2 ring-primary ring-offset-2"
                            : ""
                        )}
                        style={{
                          left: `${element.x}%`,
                          top: `${element.y}%`,
                          transform: "translate(-50%, -50%)",
                          fontSize: `${element.fontSize}px`,
                          color: element.color,
                          fontWeight: element.fontWeight,
                        }}
                        onClick={() => setActiveElementIndex(index)}
                        onMouseDown={(e) => handleElementDrag(e, index)}
                      >
                        {element.content}
                      </div>
                    );
                  } else if (element.type === "chat") {
                    return (
                      <div
                        key={element.id}
                        className={cn(
                          "absolute cursor-move",
                          activeElementIndex === index
                            ? "ring-2 ring-primary ring-offset-2"
                            : ""
                        )}
                        style={{
                          left: `${element.x}%`,
                          top: `${element.y}%`,
                          transform: "translate(-50%, -50%)",
                        }}
                        onClick={() => setActiveElementIndex(index)}
                        onMouseDown={(e) => handleElementDrag(e, index)}
                      >
                        <div className="flex flex-col items-center">
                          <Button
                            className="rounded-full flex items-center justify-center record-button"
                            style={{
                              backgroundColor: hexToRGBA(
                                element.backgroundColor,
                                element.btnOpacity || 1
                              ),
                              width: `${element.size}px`,
                              height: `${element.size}px`,
                            }}
                          >
                            <MessageSquare
                              style={{
                                width: `${element.size * element.iconScale}px`,
                                height: `${element.size * element.iconScale}px`,
                              }}
                              color={element.color}
                            />
                          </Button>
                        </div>
                      </div>
                    );
                  } else if (element.type === "subtitle" && design.subtitles) {
                    return (
                      <div
                        key={element.id}
                        className={cn(
                          "absolute cursor-move",
                          activeElementIndex === index
                            ? "ring-2 ring-primary ring-offset-2"
                            : ""
                        )}
                        style={{
                          left: `${element.x}%`,
                          top: `${element.y}%`,
                          width: `${element.width}%`,
                          height: `${element.height}%`,
                          transform: "translate(-50%, -50%)",
                          borderRadius: `${element.borderRadius}px`,

                          padding: `${element.padding}px`,

                          fontSize: `${element.fontSize}px`,
                          color: element.color,
                          fontFamily: element.fontFamily,
                          fontWeight: element.fontWeight,
                        }}
                        onClick={() => setActiveElementIndex(index)}
                        onMouseDown={(e) => handleElementDrag(e, index)}
                      >
                        <p
                          style={{
                            backgroundColor: hexToRGBA(
                              element.backgroundColor,
                              element.opacity || 1
                            ),
                            wordBreak: "break-all",
                            textAlign: "left",
                            padding: `${element.fontPadding}px`,
                            borderRadius: `${element.borderRadius}px`,
                          }}
                        >
                          示例字幕文本
                        </p>
                      </div>
                    );
                  }
                  return null;
                })}
              </div>
            </div>

            <div className="space-y-6">
              <div className="bg-card rounded-lg p-4 border">
                <h2 className="text-lg font-medium mb-4">设计选项</h2>

                <Tabs defaultValue="background">
                  <TabsList className="grid grid-cols-3 mb-4">
                    <TabsTrigger
                      value="background"
                      className="flex items-center gap-2"
                    >
                      <PaintBucket className="h-4 w-4" />
                      背景
                    </TabsTrigger>
                    <TabsTrigger
                      value="elements"
                      className="flex items-center gap-2"
                    >
                      <MoveHorizontal className="h-4 w-4" />
                      元素
                    </TabsTrigger>
                    <TabsTrigger
                      value="text"
                      className="flex items-center gap-2"
                    >
                      <Type className="h-4 w-4" />
                      文本
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="background" className="space-y-4">
                    <div>
                      <Label htmlFor="bgColor">背景颜色</Label>
                      <div className="flex items-center gap-3 mt-1">
                        <Input
                          id="bgColor"
                          type="color"
                          value={design.backgroundColor}
                          onChange={(e) =>
                            handleBackgroundColorChange(e.target.value)
                          }
                          className="w-12 h-10 p-1"
                        />
                        <Input
                          type="text"
                          value={design.backgroundColor}
                          onChange={(e) =>
                            handleBackgroundColorChange(e.target.value)
                          }
                          className="flex-1"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>
                        背景透明度
                        {design.opacity || 1}
                      </Label>
                      <Slider
                        min={0}
                        max={1}
                        step={0.1}
                        value={[design.opacity || 1]}
                        onValueChange={(values) =>
                          handleBackgroundOpacityChange(values[0])
                        }
                      />
                    </div>

                    <div>
                      <Label htmlFor="bgImage">背景图片</Label>
                      <div className="flex items-center gap-3 mt-1">
                        <Button
                          variant="outline"
                          onClick={() => fileInputRef.current?.click()}
                          className="w-full"
                        >
                          选择图片
                        </Button>
                        <input
                          ref={fileInputRef}
                          id="bgImage"
                          type="file"
                          accept="image/*"
                          onChange={handleBackgroundImageUpload}
                          className="hidden"
                        />
                      </div>
                      {design.backgroundImage && (
                        <div className="mt-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={removeImg}
                          >
                            移除背景图片
                          </Button>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="elements" className="space-y-4">
                    <div
                      className="space-y-4"
                      style={{ maxHeight: "450px", overflow: "auto" }}
                    >
                      {/* Video Element Controls */}
                      {videoElementIndex !== -1 && (
                        <div className="border rounded-md p-3">
                          <div className="flex justify-between mb-2">
                            <span className="font-medium">视频元素</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() =>
                                setActiveElementIndex(videoElementIndex)
                              }
                            >
                              选择
                            </Button>
                          </div>

                          <div className="space-y-3">
                            <div>
                              <Label htmlFor="video-width">
                                视频宽度:{" "}
                                {design.elements[videoElementIndex].width}%
                              </Label>
                              <Slider
                                id="video-width"
                                min={10}
                                max={90}
                                step={1}
                                value={[
                                  design.elements[videoElementIndex].width ||
                                    88,
                                ]}
                                onValueChange={(values) =>
                                  handleElementResize(
                                    videoElementIndex,
                                    values[0]
                                  )
                                }
                                className="mt-2"
                              />
                            </div>

                            <div>
                              <Label htmlFor="video-position">位置</Label>
                              <div className="grid grid-cols-2 gap-2 mt-1">
                                <div>
                                  <Label htmlFor="video-x" className="text-xs">
                                    水平 (X):{" "}
                                    {Math.round(
                                      design.elements[videoElementIndex].x
                                    )}
                                    %
                                  </Label>
                                  <Slider
                                    id="video-x"
                                    min={0}
                                    max={100}
                                    step={1}
                                    value={[
                                      design.elements[videoElementIndex].x,
                                    ]}
                                    onValueChange={(values) =>
                                      handleElementChange(
                                        videoElementIndex,
                                        "x",
                                        values[0]
                                      )
                                    }
                                    className="mt-1"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor="video-y" className="text-xs">
                                    垂直 (Y):{" "}
                                    {Math.round(
                                      design.elements[videoElementIndex].y
                                    )}
                                    %
                                  </Label>
                                  <Slider
                                    id="video-y"
                                    min={0}
                                    max={100}
                                    step={1}
                                    value={[
                                      design.elements[videoElementIndex].y,
                                    ]}
                                    onValueChange={(values) =>
                                      handleElementChange(
                                        videoElementIndex,
                                        "y",
                                        values[0]
                                      )
                                    }
                                    className="mt-1"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {subtitleElementIndex !== -1 && design.subtitles && (
                        <div className="border rounded-md p-4 space-y-4">
                          <div className="flex justify-between items-center">
                            <h3 className="font-medium">字幕框设置</h3>
                          </div>
                          {/* 尺寸设置 */}
                          <div>
                            <Label htmlFor="video-position">大小</Label>
                            <div className="grid grid-cols-2 gap-2 mt-1">
                              <div>
                                <Label
                                  htmlFor="video-width"
                                  className="text-xs"
                                >
                                  宽度:{" "}
                                  {Math.round(
                                    design.elements[subtitleElementIndex]
                                      .width || 80
                                  )}
                                  %
                                </Label>
                                <Slider
                                  min={20}
                                  max={100}
                                  step={1}
                                  value={[
                                    design.elements[subtitleElementIndex].width,
                                  ]}
                                  onValueChange={(values) =>
                                    handleElementChange(
                                      subtitleElementIndex,
                                      "width",
                                      values[0]
                                    )
                                  }
                                  className="mt-1"
                                />
                              </div>
                              <div>
                                <Label
                                  htmlFor="video-height"
                                  className="text-xs"
                                >
                                  高度:{" "}
                                  {Math.round(
                                    design.elements[subtitleElementIndex].height
                                  )}
                                  %
                                </Label>
                                <Slider
                                  min={5}
                                  max={90}
                                  step={1}
                                  value={[
                                    design.elements[subtitleElementIndex]
                                      .height,
                                  ]}
                                  onValueChange={(values) =>
                                    handleElementChange(
                                      subtitleElementIndex,
                                      "height",
                                      values[0]
                                    )
                                  }
                                  className="mt-1"
                                />
                              </div>
                            </div>
                          </div>

                          <div>
                            <Label htmlFor="video-position">位置</Label>
                            <div className="grid grid-cols-2 gap-2 mt-1">
                              <div>
                                <Label htmlFor="video-x" className="text-xs">
                                  水平 (X):{" "}
                                  {Math.round(
                                    design.elements[subtitleElementIndex].x
                                  )}
                                  %
                                </Label>
                                <Slider
                                  id="video-x"
                                  min={0}
                                  max={100}
                                  step={1}
                                  value={[
                                    design.elements[subtitleElementIndex].x,
                                  ]}
                                  onValueChange={(values) =>
                                    handleElementChange(
                                      subtitleElementIndex,
                                      "x",
                                      values[0]
                                    )
                                  }
                                  className="mt-1"
                                />
                              </div>
                              <div>
                                <Label htmlFor="video-y" className="text-xs">
                                  垂直 (Y):{" "}
                                  {Math.round(
                                    design.elements[subtitleElementIndex].y
                                  )}
                                  %
                                </Label>
                                <Slider
                                  id="video-y"
                                  min={0}
                                  max={100}
                                  step={1}
                                  value={[
                                    design.elements[subtitleElementIndex].y,
                                  ]}
                                  onValueChange={(values) =>
                                    handleElementChange(
                                      subtitleElementIndex,
                                      "y",
                                      values[0]
                                    )
                                  }
                                  className="mt-1"
                                />
                              </div>
                            </div>
                          </div>

                          <div>
                            <Label htmlFor="video-position">边框圆角</Label>
                            <div className="grid grid-cols-2 gap-2 mt-1">
                              <div>
                                <Label
                                  htmlFor="video-width"
                                  className="text-xs"
                                >
                                  圆角大小:
                                </Label>
                                <div className="flex items-center gap-2">
                                  <Input
                                    type="number"
                                    min={0}
                                    max={50}
                                    value={
                                      design.elements[subtitleElementIndex]
                                        .borderRadius || 8
                                    }
                                    onChange={(e) => {
                                      const value = Math.min(
                                        50,
                                        Math.max(0, Number(e.target.value))
                                      );
                                      handleElementChange(
                                        subtitleElementIndex,
                                        "borderRadius",
                                        value
                                      );
                                    }}
                                    className="w-24"
                                  />
                                  <span className="text-sm text-muted-foreground">
                                    px
                                  </span>
                                </div>
                              </div>
                              <div>
                                <Label
                                  htmlFor="video-width"
                                  className="text-xs"
                                >
                                  内边距:
                                </Label>
                                <div className="flex items-center gap-2">
                                  <Input
                                    type="number"
                                    min={0}
                                    max={32}
                                    value={
                                      design.elements[subtitleElementIndex]
                                        .padding || 16
                                    }
                                    onChange={(e) => {
                                      const value = Math.min(
                                        32,
                                        Math.max(0, Number(e.target.value))
                                      );
                                      handleElementChange(
                                        subtitleElementIndex,
                                        "padding",
                                        value
                                      );
                                    }}
                                    className="w-24"
                                  />
                                  <span className="text-sm text-muted-foreground">
                                    px
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* 背景设置 */}
                          <div className="space-y-2">
                            <Label>文字背景颜色</Label>
                            <div className="flex items-center gap-2">
                              <Input
                                type="color"
                                value={
                                  design.elements[subtitleElementIndex]
                                    .backgroundColor
                                }
                                onChange={(e) =>
                                  handleElementChange(
                                    subtitleElementIndex,
                                    "backgroundColor",
                                    e.target.value
                                  )
                                }
                                className="w-12 h-10 p-1"
                              />
                              <Input
                                type="text"
                                value={
                                  design.elements[subtitleElementIndex]
                                    .backgroundColor
                                }
                                onChange={(e) =>
                                  handleElementChange(
                                    subtitleElementIndex,
                                    "backgroundColor",
                                    e.target.value
                                  )
                                }
                                className="flex-1"
                              />
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label>
                              文字背景透明度
                              {design.elements[subtitleElementIndex].opacity ||
                                1}
                            </Label>
                            <Slider
                              min={0}
                              max={1}
                              step={0.1}
                              value={[
                                design.elements[subtitleElementIndex].opacity ||
                                  1,
                              ]}
                              onValueChange={(values) =>
                                handleElementChange(
                                  subtitleElementIndex,
                                  "opacity",
                                  values[0]
                                )
                              }
                            />
                          </div>

                          <div>
                            <Label htmlFor="video-position">
                              文字大小及边距
                            </Label>
                            <div className="grid grid-cols-2 gap-2 mt-1">
                              <div>
                                <Label
                                  htmlFor="video-width"
                                  className="text-xs"
                                >
                                  文字大小:
                                </Label>
                                <div className="flex items-center gap-2">
                                  <Input
                                    type="number"
                                    min={12}
                                    max={32}
                                    value={
                                      design.elements[subtitleElementIndex]
                                        .fontSize || 16
                                    }
                                    onChange={(e) => {
                                      const value = Math.min(
                                        32,
                                        Math.max(0, Number(e.target.value))
                                      );
                                      handleElementChange(
                                        subtitleElementIndex,
                                        "fontSize",
                                        value
                                      );
                                    }}
                                    className="w-24"
                                  />
                                  <span className="text-sm text-muted-foreground">
                                    px
                                  </span>
                                </div>
                              </div>
                              <div>
                                <Label
                                  htmlFor="video-width"
                                  className="text-xs"
                                >
                                  内边距:
                                </Label>
                                <div className="flex items-center gap-2">
                                  <Input
                                    type="number"
                                    min={0}
                                    max={32}
                                    value={
                                      design.elements[subtitleElementIndex]
                                        .fontPadding || 10
                                    }
                                    onChange={(e) => {
                                      const value = Math.min(
                                        32,
                                        Math.max(0, Number(e.target.value))
                                      );
                                      handleElementChange(
                                        subtitleElementIndex,
                                        "fontPadding",
                                        value
                                      );
                                    }}
                                    className="w-24"
                                  />
                                  <span className="text-sm text-muted-foreground">
                                    px
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label>文字颜色</Label>
                            <div className="flex items-center gap-2">
                              <Input
                                type="color"
                                value={
                                  design.elements[subtitleElementIndex].color
                                }
                                onChange={(e) =>
                                  handleElementChange(
                                    subtitleElementIndex,
                                    "color",
                                    e.target.value
                                  )
                                }
                                className="w-12 h-10 p-1"
                              />
                              <Input
                                type="text"
                                value={
                                  design.elements[subtitleElementIndex].color
                                }
                                onChange={(e) =>
                                  handleElementChange(
                                    subtitleElementIndex,
                                    "color",
                                    e.target.value
                                  )
                                }
                                className="flex-1"
                              />
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label>字体</Label>
                            <Select
                              value={
                                design.elements[subtitleElementIndex].fontFamily
                              }
                              onValueChange={(value) =>
                                handleElementChange(
                                  subtitleElementIndex,
                                  "fontFamily",
                                  value
                                )
                              }
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="选择字体" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="system-ui">
                                  系统默认
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label>字重</Label>
                            <Select
                              value={
                                design.elements[subtitleElementIndex].fontWeight
                              }
                              onValueChange={(value) =>
                                handleElementChange(
                                  subtitleElementIndex,
                                  "fontWeight",
                                  value
                                )
                              }
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="选择字重" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="normal">正常</SelectItem>
                                <SelectItem value="bold">粗体</SelectItem>
                                <SelectItem value="lighter">细体</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      )}

                      {/* Chat Button Element Controls */}
                      {chatButtonElementIndex !== -1 && (
                        <div className="border rounded-md p-3">
                          <div className="flex justify-between mb-2">
                            <span className="font-medium">聊天按钮</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() =>
                                setActiveElementIndex(chatButtonElementIndex)
                              }
                            >
                              选择
                            </Button>
                          </div>
                          <div className="space-y-3">
                            <div>
                              <Label htmlFor="button-position">位置</Label>
                              <div className="grid grid-cols-2 gap-2 mt-1">
                                <div>
                                  <Label htmlFor="button-x" className="text-xs">
                                    水平 (X):{" "}
                                    {Math.round(
                                      design.elements[chatButtonElementIndex].x
                                    )}
                                    %
                                  </Label>
                                  <Slider
                                    id="button-x"
                                    min={0}
                                    max={100}
                                    step={1}
                                    value={[
                                      design.elements[chatButtonElementIndex].x,
                                    ]}
                                    onValueChange={(values) =>
                                      handleElementChange(
                                        chatButtonElementIndex,
                                        "x",
                                        values[0]
                                      )
                                    }
                                    className="mt-1"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor="button-y" className="text-xs">
                                    垂直 (Y):{" "}
                                    {Math.round(
                                      design.elements[chatButtonElementIndex].y
                                    )}
                                    %
                                  </Label>
                                  <Slider
                                    id="button-y"
                                    min={0}
                                    max={100}
                                    step={1}
                                    value={[
                                      design.elements[chatButtonElementIndex].y,
                                    ]}
                                    onValueChange={(values) =>
                                      handleElementChange(
                                        chatButtonElementIndex,
                                        "y",
                                        values[0]
                                      )
                                    }
                                    className="mt-1"
                                  />
                                </div>
                              </div>
                            </div>
                            <div>
                              <Label>
                                按钮大小{" "}
                                {Math.round(
                                  design.elements[chatButtonElementIndex].size
                                )}
                                px
                              </Label>
                              <Slider
                                min={32}
                                max={80}
                                step={8}
                                value={[
                                  design.elements[chatButtonElementIndex].size,
                                ]}
                                onValueChange={(values) =>
                                  handleElementChange(
                                    chatButtonElementIndex,
                                    "size",
                                    values[0]
                                  )
                                }
                              />
                            </div>
                            <div>
                              <Label>按钮颜色</Label>
                              <div className="flex items-center gap-3 mt-1">
                                <Input
                                  type="color"
                                  value={
                                    design.elements[chatButtonElementIndex]
                                      .backgroundColor
                                  }
                                  onChange={(e) =>
                                    handleElementChange(
                                      chatButtonElementIndex,
                                      "backgroundColor",
                                      e.target.value
                                    )
                                  }
                                  className="w-12 h-10 p-1"
                                />
                                <Input
                                  type="text"
                                  value={
                                    design.elements[chatButtonElementIndex]
                                      .backgroundColor
                                  }
                                  onChange={(e) =>
                                    handleElementChange(
                                      chatButtonElementIndex,
                                      "backgroundColor",
                                      e.target.value
                                    )
                                  }
                                  className="flex-1"
                                />
                              </div>
                            </div>
                            <div>
                              <Label>
                                按钮透明度{" "}
                                {design.elements[chatButtonElementIndex]
                                  .btnOpacity || 1}
                              </Label>
                              <Slider
                                min={0}
                                max={1}
                                step={0.1}
                                value={[
                                  design.elements[chatButtonElementIndex]
                                    .btnOpacity,
                                ]}
                                onValueChange={(values) =>
                                  handleElementChange(
                                    chatButtonElementIndex,
                                    "btnOpacity",
                                    values[0]
                                  )
                                }
                              />
                            </div>
                            <div>
                              <Label>图标颜色</Label>
                              <div className="flex items-center gap-3 mt-1">
                                <Input
                                  type="color"
                                  value={
                                    design.elements[chatButtonElementIndex]
                                      .color
                                  }
                                  onChange={(e) =>
                                    handleElementChange(
                                      chatButtonElementIndex,
                                      "color",
                                      e.target.value
                                    )
                                  }
                                  className="w-12 h-10 p-1"
                                />
                                <Input
                                  type="text"
                                  value={
                                    design.elements[chatButtonElementIndex]
                                      .color
                                  }
                                  onChange={(e) =>
                                    handleElementChange(
                                      chatButtonElementIndex,
                                      "color",
                                      e.target.value
                                    )
                                  }
                                  className="flex-1"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Mic Button Element Controls */}
                      {micButtonElementIndex !== -1 && (
                        <div className="border rounded-md p-3">
                          <div className="flex justify-between mb-2">
                            <span className="font-medium">录音按钮</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() =>
                                setActiveElementIndex(micButtonElementIndex)
                              }
                            >
                              选择
                            </Button>
                          </div>

                          <div className="space-y-3">
                            <div>
                              <Label htmlFor="button-position">位置</Label>
                              <div className="grid grid-cols-2 gap-2 mt-1">
                                <div>
                                  <Label htmlFor="button-x" className="text-xs">
                                    水平 (X):{" "}
                                    {Math.round(
                                      design.elements[micButtonElementIndex].x
                                    )}
                                    %
                                  </Label>
                                  <Slider
                                    id="button-x"
                                    min={0}
                                    max={100}
                                    step={1}
                                    value={[
                                      design.elements[micButtonElementIndex].x,
                                    ]}
                                    onValueChange={(values) =>
                                      handleElementChange(
                                        micButtonElementIndex,
                                        "x",
                                        values[0]
                                      )
                                    }
                                    className="mt-1"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor="button-y" className="text-xs">
                                    垂直 (Y):{" "}
                                    {Math.round(
                                      design.elements[micButtonElementIndex].y
                                    )}
                                    %
                                  </Label>
                                  <Slider
                                    id="button-y"
                                    min={0}
                                    max={100}
                                    step={1}
                                    value={[
                                      design.elements[micButtonElementIndex].y,
                                    ]}
                                    onValueChange={(values) =>
                                      handleElementChange(
                                        micButtonElementIndex,
                                        "y",
                                        values[0]
                                      )
                                    }
                                    className="mt-1"
                                  />
                                </div>
                              </div>
                            </div>
                            <div>
                              <Label>
                                按钮大小{" "}
                                {Math.round(
                                  design.elements[micButtonElementIndex].size
                                )}
                                px
                              </Label>
                              <Slider
                                min={32}
                                max={80}
                                step={8}
                                value={[
                                  design.elements[micButtonElementIndex].size,
                                ]}
                                onValueChange={(values) =>
                                  handleElementChange(
                                    micButtonElementIndex,
                                    "size",
                                    values[0]
                                  )
                                }
                              />
                            </div>
                            <div>
                              <Label>按钮颜色</Label>
                              <div className="flex items-center gap-3 mt-1">
                                <Input
                                  type="color"
                                  value={
                                    design.elements[micButtonElementIndex]
                                      .backgroundColor
                                  }
                                  onChange={(e) =>
                                    handleElementChange(
                                      micButtonElementIndex,
                                      "backgroundColor",
                                      e.target.value
                                    )
                                  }
                                  className="w-12 h-10 p-1"
                                />
                                <Input
                                  type="text"
                                  value={
                                    design.elements[micButtonElementIndex]
                                      .backgroundColor
                                  }
                                  onChange={(e) =>
                                    handleElementChange(
                                      micButtonElementIndex,
                                      "backgroundColor",
                                      e.target.value
                                    )
                                  }
                                  className="flex-1"
                                />
                              </div>
                            </div>
                            <div>
                              <Label>
                                按钮透明度{" "}
                                {design.elements[micButtonElementIndex]
                                  .btnOpacity || 1}
                              </Label>
                              <Slider
                                min={0}
                                max={1}
                                step={0.1}
                                value={[
                                  design.elements[micButtonElementIndex]
                                    .btnOpacity || 1,
                                ]}
                                onValueChange={(values) =>
                                  handleElementChange(
                                    micButtonElementIndex,
                                    "btnOpacity",
                                    values[0]
                                  )
                                }
                              />
                            </div>
                            <div>
                              <Label>图标颜色</Label>
                              <div className="flex items-center gap-3 mt-1">
                                <Input
                                  type="color"
                                  value={
                                    design.elements[micButtonElementIndex].color
                                  }
                                  onChange={(e) =>
                                    handleElementChange(
                                      micButtonElementIndex,
                                      "color",
                                      e.target.value
                                    )
                                  }
                                  className="w-12 h-10 p-1"
                                />
                                <Input
                                  type="text"
                                  value={
                                    design.elements[micButtonElementIndex].color
                                  }
                                  onChange={(e) =>
                                    handleElementChange(
                                      micButtonElementIndex,
                                      "color",
                                      e.target.value
                                    )
                                  }
                                  className="flex-1"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="text" className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="font-medium">文本元素</h3>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleAddText}
                      >
                        添加文本
                      </Button>
                    </div>

                    {design.elements.filter((e) => e.type === "text").length ===
                    0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        没有文本元素，点击"添加文本"开始
                      </div>
                    ) : (
                      <div
                        className="space-y-4"
                        style={{ maxHeight: "450px", overflow: "auto" }}
                      >
                        {design.elements.map((element, index) => {
                          if (element.type !== "text") return null;

                          return (
                            <div
                              key={element.id}
                              className={cn(
                                "border rounded-md p-3",
                                activeElementIndex === index
                                  ? "border-primary"
                                  : ""
                              )}
                              onClick={() => setActiveElementIndex(index)}
                            >
                              <div className="flex justify-between mb-2">
                                <span className="font-medium">
                                  文本 #{index}
                                </span>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleRemoveElement(index)}
                                >
                                  <Trash2 className="h-4 w-4 text-destructive" />
                                </Button>
                              </div>

                              <div className="space-y-3">
                                <div>
                                  <Label htmlFor={`text-content-${index}`}>
                                    内容
                                  </Label>
                                  <Textarea
                                    id={`text-content-${index}`}
                                    value={element.content}
                                    onChange={(e) =>
                                      handleElementChange(
                                        index,
                                        "content",
                                        e.target.value
                                      )
                                    }
                                    className="mt-1"
                                  />
                                </div>

                                <div>
                                  <Label htmlFor={`text-color-${index}`}>
                                    文字颜色
                                  </Label>
                                  <div className="flex items-center gap-3 mt-1">
                                    <Input
                                      id={`text-color-${index}`}
                                      type="color"
                                      value={element.color}
                                      onChange={(e) =>
                                        handleElementChange(
                                          index,
                                          "color",
                                          e.target.value
                                        )
                                      }
                                      className="w-12 h-10 p-1"
                                    />
                                    <Input
                                      type="text"
                                      value={element.color}
                                      onChange={(e) =>
                                        handleElementChange(
                                          index,
                                          "color",
                                          e.target.value
                                        )
                                      }
                                      className="flex-1"
                                    />
                                  </div>
                                </div>

                                <div>
                                  <Label htmlFor={`text-size-${index}`}>
                                    字体大小: {element.fontSize}px
                                  </Label>
                                  <Slider
                                    id={`text-size-${index}`}
                                    min={8}
                                    max={48}
                                    step={1}
                                    value={[element.fontSize || 16]}
                                    onValueChange={(values) =>
                                      handleElementChange(
                                        index,
                                        "fontSize",
                                        values[0]
                                      )
                                    }
                                    className="mt-2"
                                  />
                                </div>

                                <div>
                                  <Label htmlFor={`text-weight-${index}`}>
                                    字体粗细
                                  </Label>
                                  <select
                                    id={`text-weight-${index}`}
                                    value={element.fontWeight}
                                    onChange={(e) =>
                                      handleElementChange(
                                        index,
                                        "fontWeight",
                                        e.target.value
                                      )
                                    }
                                    className="w-full mt-1 rounded-md border border-input bg-background px-3 py-2 text-sm"
                                  >
                                    <option value="normal">正常</option>
                                    <option value="bold">粗体</option>
                                    <option value="lighter">细体</option>
                                  </select>
                                </div>

                                <div>
                                  <Label htmlFor={`text-position-${index}`}>
                                    位置
                                  </Label>
                                  <div className="grid grid-cols-2 gap-2 mt-1">
                                    <div>
                                      <Label
                                        htmlFor={`text-x-${index}`}
                                        className="text-xs"
                                      >
                                        水平 (X): {Math.round(element.x)}%
                                      </Label>
                                      <Slider
                                        id={`text-x-${index}`}
                                        min={0}
                                        max={100}
                                        step={1}
                                        value={[element.x]}
                                        onValueChange={(values) =>
                                          handleElementChange(
                                            index,
                                            "x",
                                            values[0]
                                          )
                                        }
                                        className="mt-1"
                                      />
                                    </div>
                                    <div>
                                      <Label
                                        htmlFor={`text-y-${index}`}
                                        className="text-xs"
                                      >
                                        垂直 (Y): {Math.round(element.y)}%
                                      </Label>
                                      <Slider
                                        id={`text-y-${index}`}
                                        min={0}
                                        max={100}
                                        step={1}
                                        value={[element.y]}
                                        onValueChange={(values) =>
                                          handleElementChange(
                                            index,
                                            "y",
                                            values[0]
                                          )
                                        }
                                        className="mt-1"
                                      />
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          </div>
        </div>
      </PageTransition>

      <LoadingDialog open={isLoading} message="正在加载项目..." />
      <LoadingDialog open={isSaving} message="正在保存设计..." />
    </MainLayout>
  );
};

export default ProjectDesign;
