import { useState, useEffect, useRef } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Slider } from "@/components/ui/slider";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import PageTransition from "@/components/ui/PageTransition";
import MainLayout from "@/components/layout/MainLayout";
import LoadingDialog from "@/components/ui/LoadingDialog";
import {
  Mic,
  ArrowLeft,
  Save,
  Image,
  Type,
  PaintBucket,
  Trash2,
  MoveHorizontal,
  MoveVertical,
  MessageSquare,
  Send,
} from "lucide-react";
import {
  cn,
  convertNumberFormat,
  generateRandomState,
  hexToRGBA,
} from "@/lib/utils";
import axios from "axios";
import { RProject, type Project as ProjectFormat } from "@/request/project";
import store from "store2";
import { RDesign, type Design } from "@/request/design";
import { useRequest } from "ahooks";
import type { Response } from "../../vite-env";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

interface Element {
  id: string;
  type: "text" | "video" | "button" | "subtitle" | "chat";
  x: number;
  y: number;
  width?: number;
  height?: number;
  content?: string;
  fontSize?: number;
  color?: string;
  fontWeight?: string;
  backgroundColor?: string;
  size?: number;
  iconScale?: number;
  // 字幕框特有属性
  padding?: number;
  borderRadius?: number;
  opacity?: number;
  fontFamily?: string;
  fontPadding?: number;
  btnOpacity?: number;
}

interface ProjectDesign {
  id: string;
  projectId: string;
  backgroundColor: string;
  backgroundImage: string | null;
  backgroundUrl: string | null;
  elements: Element[];
  aspectRatio: string;
  subtitles: boolean;
  scrollSubtitles: boolean;
  opacity?: number;
  version: number;
}

interface Project {
  id: string;
  title: string;
  avatar: {
    id: string;
    type: "video" | "image";
    thumbnail: string;
  };
}
interface HistoryItem {
  question: string | null;
  answer: string | null;
}

const defaultDesign: ProjectDesign = {
  id: "",
  projectId: "",
  backgroundColor: "#ffffff",
  backgroundImage: null,
  opacity: 1,
  elements: [
    // Default video element
    {
      id: "video-element",
      type: "video",
      x: 50,
      y: 50,
      width: 28,
      height: 0, // aspect ratio controlled
    },
    // Default mic button
    {
      id: "chat-button",
      type: "chat",
      x: 47,
      y: 89,
      size: 40,
      backgroundColor: "#cccccc",
      color: "#000000",
      iconScale: 0.5,
      btnOpacity: 1,
    },
    {
      id: "mic-button",
      type: "button",
      x: 53,
      y: 89,
      size: 40,
      backgroundColor: "#cccccc",
      color: "#000000",
      iconScale: 0.5,
      btnOpacity: 1,
    },
    {
      id: "subtitle-container",
      type: "subtitle",
      x: 50,
      y: 56,
      width: 25, // 百分比
      height: 50, // 百分比
      backgroundColor: "#357eb6",
      opacity: 0.5,
      padding: 16,
      borderRadius: 8,
      fontSize: 16,
      color: "#FFFFFF",
      fontFamily: "system-ui",
      fontWeight: "normal",
      fontPadding: 10,
    },
  ],
  aspectRatio: "16:9",
  scrollSubtitles: false,
  subtitles: true,
  version: 1,
  backgroundUrl: "",
};
let sessionid = null;

const ProjectDesign = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [project, setProject] = useState<Project | null>(null);
  const [design, setDesign] = useState<ProjectDesign>({
    ...defaultDesign,
    projectId: projectId || "",
  });
  const [activeElementIndex, setActiveElementIndex] = useState<number | null>(
    null
  );
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const designContainerRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [scale, setScale] = useState((window.innerHeight / 450).toFixed(2));
  const [isTouching, setIsTouching] = useState(false);

  //重构client
  const [useTurn] = useState(false);
  const [playBtn, setPlayBtn] = useState(false);
  const [fetchBaseUrl, setFetchBaseUrl] = useState("");
  const [fetchWsUrl, setFetchWsUrl] = useState(import.meta.env.VITE_WS_URL);
  //const [fetchWsUrl, setFetchWsUrl] = useState("wss://tai.tslsmart.com:10096/");

  const [welcomeText] = useState(
    "您好，我是智能医保助手，请问有什么可以帮助您的吗"
  );
  const [chartHistory, setChartHistory] = useState<HistoryItem[]>([]);

  var pc = null;
  var wsconnecter = null;
  let offline_text = "";
  let rec_text = "";
  //@ts-ignore
  var rec = Recorder({
    type: "pcm",
    bitRate: 16,
    sampleRate: 16000,
    onProcess: recProcess,
  });
  var sampleBuf = new Int16Array();
  const [isCanceled, setIsCanceled] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const chartRef = useRef(null);
  //聊天逻辑
  const [isChatMode, setIsChatMode] = useState(false);
  const [inputMessage, setInputMessage] = useState("");

  const handleSendMessage = async () => {
    if (inputMessage.trim()) {
      // 处理发送消息的逻辑
      setChartHistory((prevChartHistory: any) => {
        return [
          ...prevChartHistory,
          { question: inputMessage.trim(), answer: "" },
        ];
      });
      const data = {
        query: inputMessage.trim(),
        //query: '回答字数100字以内，输出纯文本格式，不要markdown格式 不需要*号' + rec_text.trim(),
        knowledge_base_name: "yibao",
        top_k: 10,
        history_len: -1,
        history: [],
        stream: true,
        model_name: "Bit-LLM-Small",
        temperature: 0.5,
        max_tokens: 0,
        prompt_name: "default",
      };
      await postToLLM("/chat/knowledge_base_chat", data);
      setInputMessage("");
    }
  };

  function recProcess(
    buffer,
    powerLevel,
    bufferDuration,
    bufferSampleRate,
    newBufferIdx,
    asyncEnd
  ) {
    if (wsconnecter) {
      var data_48k = buffer[buffer.length - 1];

      var array_48k = new Array(data_48k);
      //@ts-ignore
      var data_16k = Recorder.SampleData(
        array_48k,
        bufferSampleRate,
        16000
      ).data;

      sampleBuf = Int16Array.from([...sampleBuf, ...data_16k]);
      var chunk_size = 960; // for asr chunk_size [5, 10, 5]
      while (sampleBuf.length >= chunk_size) {
        let sendBuf = sampleBuf.slice(0, chunk_size);
        sampleBuf = sampleBuf.slice(chunk_size, sampleBuf.length);
        console.log("asr sendBuf:");
        wsconnecter.wsSend(sendBuf);
      }
    }
  }
  function start() {
    const config: any = {
      sdpSemantics: "unified-plan",
    };
    if (useTurn) {
      config.iceServers = [
        {
          urls: ["stun:stun.l.google.com:19302"],
        },
        {
          urls: ["turn:47.94.164.210:3478?transport=udp"],
          username: "tslapp",
          credential: "3A2a9ePPTnA",
        },
      ];
    }
    pc = new RTCPeerConnection(config);
    pc.addEventListener("track", (evt: any) => {
      if (evt.track.kind == "video") {
        const videoElement: any = document.getElementById("video");
        console.log("start video");
        videoElement.srcObject = evt.streams[0];
        const promise = videoElement.play();
        if (promise !== undefined) {
          promise
            .then((_) => {
              console.log("Autoplay started!");
              // Autoplay started!
            })
            .catch((error) => {
              console.error("Autoplay was prevented.", error);
              setPlayBtn(true);
              //playButton.style.display = "block";
              // Autoplay was prevented.
              // Show a "Play" button so that user can start playback.
            });
        }
      }
    });
    negotiate().then(() => {
      //document.getElementById('stop').style.display = 'inline-block';
      document.getElementById("canvas").style.display = "inline-block";

      setTimeout(() => {
        sendToHuman(welcomeText, false);
        setTimeout(() => {
          setChartHistory((prevChartHistory) => [
            ...prevChartHistory,
            { answer: welcomeText, question: null },
          ]);
        }, 1000);
      }, 2000);
    });
  }

  async function postToLLM(url, data) {
    const headers = {
      "Content-Type": "application/json",
      timeout: 60000,
    };

    const response = await fetch(url, {
      method: "POST",
      //@ts-ignore
      headers: headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let done = false;
    let resultBuffer = ""; // 缓存接收到的数据
    let sentenceBuffer = ""; // 用于临时缓存当前句子
    let isLast = false;

    while (!done) {
      const { value, done: readerDone } = await reader.read();
      done = readerDone;
      resultBuffer += decoder.decode(value, { stream: true }); // 将当前流解码并拼接

      while (true) {
        const jsonStartIndex = resultBuffer.indexOf("data: {");
        const jsonEndIndex = resultBuffer.indexOf("}", jsonStartIndex);

        if (jsonStartIndex !== -1 && jsonEndIndex !== -1) {
          const jsonString = resultBuffer.slice(
            jsonStartIndex + 6,
            jsonEndIndex + 1
          );
          try {
            const jsonData = JSON.parse(jsonString);
            const text = jsonData.answer; // 提取 'text' 字段的内容
            if (text) {
              isLast = true;
              sentenceBuffer += text; // 拼接文本到当前句子缓存
              console.log("=====text", text);
              setChartHistory((prevChartHistory: any) => {
                return [
                  ...prevChartHistory.slice(0, -1),
                  {
                    answer:
                      prevChartHistory[prevChartHistory.length - 1].answer +
                      text,
                    question:
                      prevChartHistory[prevChartHistory.length - 1].question,
                  },
                ];
              });

              const sentenceEndIndex = sentenceBuffer.search(/[。！？\n]/);
              if (sentenceEndIndex !== -1) {
                const completeSentence = sentenceBuffer
                  .slice(0, sentenceEndIndex + 1)
                  .trim();
                if (completeSentence) {
                  sendToHuman(convertNumberFormat(completeSentence), false);
                }
                sentenceBuffer = sentenceBuffer
                  .slice(sentenceEndIndex + 1)
                  .trim();
              }
            }
          } catch (error) {
            console.error("JSON 解析错误:", error);
          }
          resultBuffer = resultBuffer.slice(jsonEndIndex + 1);
        } else {
          break;
        }
      }
    }
    if (sentenceBuffer?.length) {
      sendToHuman(convertNumberFormat(sentenceBuffer));
    }

    reader.releaseLock();
  }

  function sendToHuman(message, interrupt = false) {
    console.log("sendToHuman:", message, interrupt, sessionid);
    //fetch('http://***********:8010/human', {
    fetch(`${fetchBaseUrl}/human`, {
      body: JSON.stringify({
        text: message,
        type: "echo",
        interrupt: interrupt,
        sessionid: parseInt(sessionid),
      }),
      headers: {
        "Content-Type": "application/json",
      },
      method: "POST",
    });
  }

  function negotiate() {
    pc.addTransceiver("video", { direction: "recvonly" });
    pc.addTransceiver("audio", { direction: "recvonly" });
    return pc
      .createOffer()
      .then((offer) => {
        return pc.setLocalDescription(offer);
      })
      .then(() => {
        // wait for ICE gathering to complete
        return new Promise((resolve) => {
          if (pc.iceGatheringState === "complete") {
            //@ts-ignore
            resolve();
          } else {
            const checkState = () => {
              if (pc.iceGatheringState === "complete") {
                pc.removeEventListener("icegatheringstatechange", checkState);
                //@ts-ignore
                resolve();
              }
            };
            pc.addEventListener("icegatheringstatechange", checkState);
          }
        });
      })
      .then(() => {
        var offer = pc.localDescription;
        //return fetch('http://***********:8010/offer', {
        return fetch(`${fetchBaseUrl}/offer`, {
          body: JSON.stringify({
            sdp: offer.sdp,
            type: offer.type,
          }),
          headers: {
            "Content-Type": "application/json",
          },
          method: "POST",
        });
      })
      .then((response) => {
        if (response?.status == 500) {
          return Promise.reject({ status: 500 });
        } else {
          return response.json();
        }
      })
      .then((answer) => {
        sessionid = answer.sessionid;
        return pc.setRemoteDescription(answer);
      })
      .catch((e) => {
        console.error("======fetch error", e);
        if (e?.status == 500) {
          alert("服务器最大并发数已达上限，请联系管理员处理");
        } else {
          const redirectUri = encodeURIComponent(window.location.href);
          const state = generateRandomState();
          window.location.href = `${fetchBaseUrl}/Auth.html?redirect_uri=${redirectUri}&state=${state}`;
        }
      });
  }
  function allowPlay() {
    const videoElement: any = document.getElementById("video");
    videoElement
      .play()
      .then(() => {
        setPlayBtn(false);
      })
      .catch((error) => {
        console.log("Playback failed:", error);
      });
  }

  function startRecording() {
    console.log("录音开始");

    if (wsconnecter) {
      wsconnecter?.wsStop();
      wsconnecter = null;
    }
    wsconnecter = new WebSocketConnectMethod({
      msgHandle: getJsonMessage,
      stateHandle: getConnState,
    });
    const ret = wsconnecter.wsStart(fetchWsUrl);
    rec.open(function () {
      if (wsconnecter) {
        rec.start();
      }
    });
  }

  function stopRecording() {
    console.log("录音结束");
    try {
      if (wsconnecter) {
        rec.stop(undefined, undefined, true);
        var chunk_size = new Array(5, 10, 5);
        var request = {
          chunk_size: chunk_size,
          wav_name: "h5",
          is_speaking: false,
          chunk_interval: 10,
          mode: "offline",
        };

        if (sampleBuf.length > 0) {
          wsconnecter.wsSend(sampleBuf);
          sampleBuf = new Int16Array();
        }
        wsconnecter.wsSend(JSON.stringify(request));
      }
    } catch (error) {
      console.log("err");
      console.log(error);
    }
  }

  function getJsonMessage(jsonMsg) {
    let rectxt = JSON.parse(jsonMsg.data)["text"];
    let asrmodel = JSON.parse(jsonMsg.data)["mode"];
    let is_final = JSON.parse(jsonMsg.data)["is_final"];

    if (asrmodel == "2pass-offline" || asrmodel == "offline") {
      offline_text = offline_text + rectxt;
      rec_text = offline_text;
    } else {
      rec_text = rec_text;
    }

    rec_text = rec_text.replace(/<\|.*?\|>/g, "");
    console.log("is final:", is_final);
    if (is_final == true) {
      wsconnecter.wsStop();
      sendToLLM();
    }
    console.log("asr rec_text:", rec_text);
  }
  function isStopCommand(input) {
    const stopWords = [
      "停止",
      "结束",
      "暂停",
      "退出",
      "停下",
      "停掉",
      "停止一下",
      "停",
      "关掉",
      "关闭",
      "终止",
      "别说了",
    ];

    // 将输入转换为小写，并去掉前后空格
    input = input.trim();

    // 判断输入的文本是否包含停止相关的词汇
    return stopWords.some((word) => input.includes(word));
  }
  async function sendToLLM() {
    if (!isCanceled) {
      if (isStopCommand(rec_text)) {
        sendToHuman("请问还有什么可以帮助您的？", true);
        setChartHistory((prevChartHistory) => [
          ...prevChartHistory,
          { answer: "请问还有什么可以帮助您的？", question: null },
        ]);
      } else if (rec_text && !isSpeaking && rec_text.length > 4) {
        console.log("chatLLM:", rec_text.trim());
        setChartHistory((prevChartHistory: any) => {
          return [...prevChartHistory, { question: rec_text, answer: "" }];
        });
        const data = {
          query: rec_text.trim(),
          //query: '回答字数100字以内，输出纯文本格式，不要markdown格式 不需要*号' + rec_text.trim(),
          knowledge_base_name: "yibao",
          top_k: 10,
          history_len: -1,
          history: [],
          stream: true,
          model_name: "Bit-LLM-Small",
          temperature: 0.5,
          max_tokens: 0,
          prompt_name: "default",
        };
        await postToLLM("/chat/knowledge_base_chat", data);
      }
    }
    console.log("清理rec_text");
    rec_text = "";
    offline_text = "";
  }
  function getConnState(connState) {
    if (connState === 0) {
    } else if (connState === 1) {
      //stop();
    } else if (connState === 2) {
      stop();
    }
  }
  function stop() {
    setTimeout(() => {
      pc.close();
    }, 500);

    document.getElementById("canvas").style.display = "none";
  }

  function recordVoice() {
    startRecording();
  }
  const { run: runDesign } = useRequest(RDesign, {
    manual: true,
    onSuccess: (data: Response<Design>) => {
      if (data?.data?.settings) {
        const result = data.data.settings;
        try {
          const designJson = JSON.parse(result);
          console.log("=====designJson", designJson);

          if (designJson.elements)
            setDesign({
              ...designJson,
              projectId,
              id: data.data.id,
              backgroundImage: data?.data?.backgroundUrl,
            });
        } catch (e) {
          console.error("设计数据转化失败", e);
        }
      }
    },
    onError: (e) => {
      console.error("获取原始数据失败", e);
    },
  });

  const { run: runProject } = useRequest(RProject, {
    manual: true,
    onSuccess: (data: Response<ProjectFormat>) => {
      if (data.data.publicUrl) {
        setFetchBaseUrl(data.data.publicUrl);
      }
    },
    onError: (e) => {
      console.error("获取原始数据失败", e);
    },
  });

  useEffect(() => {
    function debounce(func, wait) {
      let timeout;
      return function () {
        clearTimeout(timeout);
        timeout = setTimeout(func, wait);
      };
    }
    const handleResize = debounce(function () {
      setScale((window.innerHeight / 450).toFixed(2));
      // 在这里添加你希望在窗口大小改变时执行的代码
    }, 250);

    window.addEventListener("resize", handleResize); // 等待250毫秒后执行函数

    return () => {
      window.removeEventListener("resize", handleResize);
      pc?.close();
      const videoElement = document.getElementById("video");
      if (videoElement) {
        cleanupVideoListeners(videoElement);
      }
    };
  }, []);

  useEffect(() => {
    const root = document.getElementById("root");
    //@ts-ignore
    root.style.padding = 0;
    //@ts-ignore
    root.style.margin = 0;
    const observer = new MutationObserver((mutations) => {
      chartRef.current.scrollTo({
        top: chartRef.current.scrollHeight,
        behavior: "smooth",
      });
    });

    observer.observe(chartRef.current, {
      childList: true,
      subtree: true,
      characterData: true,
    });

    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    runDesign(projectId);
    runProject(projectId);
  }, []);
  useEffect(() => {
    if (fetchBaseUrl) {
      start();
      //确保视频元素已加载到 DOM 中
      const videoElement = document.getElementById("video");
      if (videoElement) {
        setupVideoListeners(videoElement, playBtn);
      }
    }
  }, [fetchBaseUrl]);

  // 设置视频事件监听器
  const setupVideoListeners = (videoElement, playBtn) => {
    // 尝试自动播放
    const attemptAutoPlay = () => {
      videoElement
        .play()
        .then(() => {
          console.log("视频自动播放成功");
          if (playBtn) {
            playBtn.style.display = "none"; // 隐藏按钮
          }
        })
        .catch((err) => {
          console.error("视频自动播放失败:", err);
          if (playBtn) {
            playBtn.style.display = "block"; // 显示按钮
          }
        });
    };

    // 监听视频播放事件
    videoElement.addEventListener("play", () => {
      if (playBtn) {
        playBtn.style.display = "none"; // 隐藏按钮
      }
    });

    // 监听视频错误事件
    videoElement.addEventListener("error", () => {
      if (playBtn) {
        playBtn.style.display = "block"; // 显示按钮
      }
    });

    // 初始尝试自动播放
    attemptAutoPlay();
  };

  // 清理视频事件监听器
  const cleanupVideoListeners = (videoElement) => {
    videoElement.removeEventListener("play", () => {});
    videoElement.removeEventListener("error", () => {});
  };

  const changeToVoiceMode = () => {
    setIsChatMode(false);
    setInputMessage("");
  };

  return (
    <div
      className="full-page-preview"
      style={{ width: "100vw", height: "100vh" }}
    >
      {playBtn && (
        <button
          onClick={allowPlay}
          style={{
            position: "fixed",
            top: "50%",
            left: "50%",
            transform: "translate(-50%,-50%)",
            padding: "10px 20px",
            fontSize: "16px",
            backgroundColor: "#4C82E6",
            color: "white",
            border: "none",
            borderRadius: "5px",
            cursor: "pointer",
            zIndex: 999,
          }}
        >
          由于浏览器安全策略限制，请点击此按钮允许数字人自动播放。
        </button>
      )}
      <div
        ref={designContainerRef}
        style={{
          height: "100vh",
          position: "relative",

          aspectRatio: design.aspectRatio.replace(":", "/"),
          backgroundColor: hexToRGBA(
            design.backgroundColor,
            design.opacity || 1
          ),
          backgroundImage: design.backgroundImage
            ? `url(${design.backgroundImage})`
            : "none",
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        {/* Elements */}
        {design.elements.map((element, index) => {
          // Render different element types
          if (element.type === "video") {
            return (
              <div
                key={element.id}
                className={cn("absolute cursor-move")}
                style={{
                  left: `${element.x}%`,
                  top: `${element.y}%`,
                  width: `${element.width}%`,
                }}
                // onClick={() => setActiveElementIndex(index)}
                // onMouseDown={(e) => handleElementDrag(e, index)}
              >
                <div
                  style={{ position: "relative" }}
                  className="overflow-hidden"
                >
                  {isChatMode && (
                    <div
                      className="fixed bottom-0 left-0 right-0"
                      style={{
                        position: "absolute",
                        zIndex: 100,
                        background: "rgba(0, 0, 0, 0.5)",
                        backdropFilter: "blur(10px)",
                        height: "10%",
                        display: "flex",
                        alignItems: "center",
                        padding: `${8 * Number(scale)}px`,
                      }}
                    >
                      <div className="flex items-center  max-w-xl mx-auto">
                        <button className="rounded-full bg-[rgba(255,255,255,0)] flex items-center justify-center">
                          <Mic
                            className="w-6 h-6 text-white"
                            style={{
                              width: `${16 * Number(scale)}px`,
                              height: `${16 * Number(scale)}px`,
                            }}
                            onClick={changeToVoiceMode}
                          />
                        </button>
                        <div
                          className="flex-1 flex items-center bg-[rgba(255,255,255,0.2)] rounded-full pr-1"
                          style={{ height: `${20 * Number(scale)}px` }}
                        >
                          <input
                            type="text"
                            className="flex-1 bg-transparent px-4 py-3 text-white placeholder-white/70 outline-none"
                            style={{ fontSize: `${12 * Number(scale)}px` }}
                            placeholder="请输入"
                            value={inputMessage}
                            onChange={(e) => setInputMessage(e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === "Enter" && inputMessage.trim()) {
                                handleSendMessage();
                              }
                            }}
                          />
                          <button
                            onClick={handleSendMessage}
                            className="w-8 h-8 rounded-full bg-[#8C9EFF] flex items-center justify-center"
                            disabled={!inputMessage.trim()}
                            style={{
                              width: `${18 * Number(scale)}px`,
                              height: `${18 * Number(scale)}px`,
                            }}
                          >
                            <Send
                              className="w-4 h-4 text-white"
                              style={{
                                width: `${14 * Number(scale)}px`,
                                height: `${14 * Number(scale)}px`,
                              }}
                            />
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                  <AspectRatio ratio={9 / 16} className="w-full">
                    <video
                      id="video"
                      className="w-full h-full object-cover"
                      autoPlay={true}
                      playsInline={true}
                    ></video>
                    <canvas
                      id="canvas"
                      style={{ width: "1px", height: "1px", display: "none" }}
                    ></canvas>
                    <audio id="audio" autoPlay={true}></audio>
                  </AspectRatio>
                </div>
              </div>
            );
          } else if (element.type === "button") {
            return (
              <>
                {!isChatMode && (
                  <div
                    key={element.id}
                    className={cn("absolute cursor-move")}
                    style={{
                      left: `${element.x}%`,
                      top: `${element.y}%`,
                      transform: `scale(${scale})`,
                    }}
                  >
                    <div className="flex flex-col items-center">
                      <Button
                        className={cn(
                          "rounded-full flex items-center justify-center transition-all duration-300"
                        )}
                        style={{
                          backgroundColor: hexToRGBA(
                            element.backgroundColor,
                            element.btnOpacity || 1
                          ),
                          width: `${element.size}px`,
                          height: `${element.size}px`,
                        }}
                        onMouseDown={() => {
                          recordVoice();
                        }}
                        onMouseUp={() => {
                          stopRecording();
                        }}
                      >
                        <Mic
                          style={{
                            width: `${element.size * element.iconScale}px`,
                            height: `${element.size * element.iconScale}px`,
                          }}
                          color={element.color}
                        />
                      </Button>
                    </div>
                  </div>
                )}
              </>
            );
          } else if (element.type === "text") {
            return (
              <div
                key={element.id}
                className={cn(
                  "absolute cursor-move select-none",
                  activeElementIndex === index
                    ? "ring-2 ring-primary ring-offset-2"
                    : ""
                )}
                style={{
                  left: `${element.x}%`,
                  top: `${element.y}%`,
                  transform: `scale(${scale})`,
                  fontSize: `${element.fontSize}px`,
                  color: element.color,
                  fontWeight: element.fontWeight,
                }}
                // onClick={() => setActiveElementIndex(index)}
                // onMouseDown={(e) => handleElementDrag(e, index)}
              >
                {element.content}
              </div>
            );
          } else if (element.type === "chat") {
            return (
              <>
                {!isChatMode && (
                  <div
                    key={element.id}
                    className={cn("absolute cursor-move")}
                    style={{
                      left: `${element.x}%`,
                      top: `${element.y}%`,
                      transform: `scale(${scale})`,
                    }}
                  >
                    <div className="flex flex-col items-center">
                      <Button
                        className="rounded-full flex items-center justify-center record-button"
                        style={{
                          backgroundColor: hexToRGBA(
                            element.backgroundColor,
                            element.btnOpacity || 1
                          ),
                          width: `${element.size}px`,
                          height: `${element.size}px`,
                        }}
                        onClick={() => {
                          setIsChatMode(true);
                        }}
                      >
                        <MessageSquare
                          style={{
                            width: `${element.size * element.iconScale}px`,
                            height: `${element.size * element.iconScale}px`,
                          }}
                          color={element.color}
                        />
                      </Button>
                    </div>
                  </div>
                )}
              </>
            );
          } else if (element.type === "subtitle" && design.subtitles) {
            return (
              <div
                key={element.id}
                id="chat-container"
                className={cn("absolute cursor-move")}
                style={{
                  left: `${element.x}%`,
                  top: `${element.y}%`,
                  width: `${element.width}%`,
                  height: `${element.height}%`,
                  borderRadius: `${element.borderRadius * Number(scale)}px`,

                  padding: `${element.padding * Number(scale)}px`,

                  fontSize: `${element.fontSize * Number(scale)}px`,
                  color: element.color,
                  fontFamily: element.fontFamily,
                  fontWeight: element.fontWeight,
                }}
              >
                <div
                  id="chat-messages"
                  ref={chartRef}
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    gap: "10px",
                    maxHeight: "100%",
                    overflowY: "auto",
                    scrollPaddingBottom: "20px",
                  }}
                >
                  {chartHistory.map((item, index) => {
                    return (
                      <>
                        {item.question && (
                          <div
                            key={index + "question"}
                            style={{
                              backgroundColor: hexToRGBA(
                                element.backgroundColor,
                                element.opacity || 1
                              ),

                              padding: `${
                                element.fontPadding * Number(scale)
                              }px`,
                              fontSize: `${element.fontSize * Number(scale)}px`,
                              color: element.color,
                              width: "fit-content",
                              maxWidth: "90%",
                              alignSelf: "flex-end",
                              textAlign: "left",
                              borderRadius: `${element.borderRadius}px`,
                            }}
                          >
                            <ReactMarkdown remarkPlugins={[remarkGfm]}>
                              {item.question}
                            </ReactMarkdown>
                          </div>
                        )}
                        {item.answer && (
                          <div
                            key={index + "answer"}
                            style={{
                              backgroundColor: hexToRGBA(
                                element.backgroundColor,
                                element.opacity || 1
                              ),

                              padding: `${
                                element.fontPadding * Number(scale)
                              }px`,
                              fontSize: `${element.fontSize * Number(scale)}px`,
                              color: element.color,
                              width: "fit-content",
                              maxWidth: "90%",
                              alignSelf: "flex-start",
                              textAlign: "left",
                              borderRadius: `${element.borderRadius}px`,
                            }}
                          >
                            <ReactMarkdown remarkPlugins={[remarkGfm]}>
                              {item.answer}
                            </ReactMarkdown>
                          </div>
                        )}
                      </>
                    );
                  })}
                </div>
              </div>
            );
          }
          return null;
        })}
      </div>
    </div>
  );
};

export default ProjectDesign;
