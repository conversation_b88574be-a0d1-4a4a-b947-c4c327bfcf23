import { useEffect, useState } from "react";
import CustomMainLayout from "@/components/layout/CustomMainLayout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { PlusCircle, Server, Trash, RefreshCcwIcon } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogT<PERSON>le,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import type { Response } from "../vite-env";
import { useRequest } from "ahooks";
import dayjs from "dayjs";
import {
  AResource,
  type Resource,
  Resources,
  ResourceList,
  RResource,
  UResource,
} from "@/request/resource";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

const serverFormSchema = z.object({
  name: z.string().min(1, { message: "服务器名称不能为空" }),
  ip: z.string().min(1, { message: "IP地址不能为空" }),
  port: z.string().min(1, { message: "端口不能为空" }),
  maxSessions: z.coerce.number().min(1, { message: "最大并发用户数必须大于0" }),
  status: z.enum(["Running", "Stopped"]).default("Stopped"),
});

const ResourceManagement = () => {
  const [servers, setServers] = useState<Resource[]>([]);

  const [isAddServerDialogOpen, setIsAddServerDialogOpen] = useState(false);
  const { toast } = useToast();
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(0);
  const [selectedResource, setSelectedResource] = useState<Resource | null>(
    null
  );

  const form = useForm<z.infer<typeof serverFormSchema>>({
    resolver: zodResolver(serverFormSchema),
    defaultValues: {
      name: "",
      ip: "",
      port: "",
      maxSessions: 100,
      status: "Stopped",
    },
  });

  const { run: runAdd } = useRequest(AResource, {
    manual: true,
    onSuccess: (data: Response<Resource>) => {
      if (currentPage != 1) {
        setCurrentPage(1);
      } else {
        runAll({ pageNumber: currentPage, pageSize: itemsPerPage });
      }
      toast({
        title: "服务器节点已添加",
        description: `服务器已成功添加到系统`,
      });
    },
    onFinally: () => {
      setSelectedResource(null);
      setIsAddServerDialogOpen(false);
      form.reset();
    },
  });

  const { run: runAll } = useRequest(Resources, {
    manual: true,
    onSuccess: (data: Response<ResourceList>) => {
      if (data.data.items.length == 0 && currentPage > 1) {
        setCurrentPage(currentPage - 1);
      } else {
        setServers([...data.data.items]);
        setTotalPages(Math.ceil(data.data.total / itemsPerPage));
      }
    },
  });
  const { run: runDelete } = useRequest(RResource, {
    manual: true,
    onSuccess: () => {
      toast({
        title: "节点已删除",
        description: "节点已成功从系统中删除",
      });
      runAll({ pageNumber: currentPage, pageSize: itemsPerPage });
    },
  });
  const { run: runUpdate } = useRequest(UResource, {
    manual: true,
    onSuccess: (data: Response<Resource>, params) => {
      runAll({ pageNumber: currentPage, pageSize: itemsPerPage });
      toast({
        title: "节点已保存",
        description: `用户已成功保存`,
      });
    },
    onError(e, params) {
      toast({
        title: "保存用户失败",
        description: "无法保存到服务器，请稍后再试",
        variant: "destructive",
      });
    },
    onFinally(params, data, e) {
      setSelectedResource(null);
      setIsAddServerDialogOpen(false);
      form.reset();
    },
  });

  const getPageNumbers = () => {
    const pageNumbers = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total pages are less than max visible
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // Always show first page
      pageNumbers.push(1);

      // Calculate start and end for visible pages
      let start = Math.max(2, currentPage - 1);
      let end = Math.min(totalPages - 1, currentPage + 1);

      // Adjust if we're at the beginning
      if (currentPage <= 2) {
        end = Math.min(totalPages - 1, maxVisiblePages - 1);
      }

      // Adjust if we're at the end
      if (currentPage >= totalPages - 2) {
        start = Math.max(2, totalPages - maxVisiblePages + 2);
      }

      // Add ellipsis after first page if needed
      if (start > 2) {
        pageNumbers.push("ellipsis");
      }

      // Add visible page numbers
      for (let i = start; i <= end; i++) {
        pageNumbers.push(i);
      }

      // Add ellipsis before last page if needed
      if (end < totalPages - 1) {
        pageNumbers.push("ellipsis");
      }

      // Always show last page
      pageNumbers.push(totalPages);
    }

    return pageNumbers;
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleServerStatusToggle = (serverId: string, status: string) => {
    setServers(
      servers.map((server) =>
        server.id === serverId
          ? {
              ...server,
              status: server.status === "Running" ? "Running" : "Stopped",
            }
          : server
      )
    );
    runUpdate(serverId, {
      status: status === "Running" ? "Stopped" : "Running",
    });
  };

  const handleSaveServerConfig = (server: Resource) => {
    setIsAddServerDialogOpen(true);
    setSelectedResource({ ...server });
    form.setValue("name", server.name);
    form.setValue("ip", server.ip);
    form.setValue("port", server.port.toString());
    form.setValue("maxSessions", server.maxSessions);
    form.setValue("status", server.status);
  };

  const handleAddServer = (values: z.infer<typeof serverFormSchema>) => {
    const newServer: Resource = {
      name: values.name,
      ip: values.ip,
      //@ts-ignore
      port: Number(values.port),
      maxSessions: values.maxSessions,
      status: values.status,
    };
    if (selectedResource) {
      runUpdate(selectedResource.id, newServer);
    } else {
      runAdd(newServer);
    }
  };

  const handleDeleteServer = (serverId: string) => {
    runDelete(serverId);
  };

  const refresh = () => {
    if (currentPage == 1) {
      runAll({ pageSize: itemsPerPage, pageNumber: 1 });
    } else {
      setCurrentPage(1);
    }
  };
  useEffect(() => {
    runAll({ pageSize: itemsPerPage, pageNumber: currentPage });
  }, [currentPage]);

  return (
    <CustomMainLayout>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <Server className="h-5 w-5" />
          服务器节点管理
        </h2>
        <Button className="flex items-center gap-2" onClick={refresh}>
          <RefreshCcwIcon className="h-4 w-4" />
          刷新
        </Button>
        {/* <Dialog
          open={isAddServerDialogOpen}
          onOpenChange={setIsAddServerDialogOpen}
        >
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <PlusCircle className="h-4 w-4" />
              添加服务器节点
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>添加新服务器节点</DialogTitle>
              <DialogDescription>
                填写以下信息以添加新的服务器节点到系统中
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(handleAddServer)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>服务器名称</FormLabel>
                      <FormControl>
                        <Input placeholder="输入服务器名称" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="ip"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>IP 地址</FormLabel>
                      <FormControl>
                        <Input placeholder="如: *************" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="port"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>端口</FormLabel>
                      <FormControl>
                        <Input placeholder="如: 8080" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="maxSessions"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>最大并发用户数</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormDescription>
                        此服务器节点可以同时支持的最大用户数
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel>启用状态</FormLabel>
                        <FormDescription>
                          设置服务器节点是否立即启用
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value === "Running"}
                          onCheckedChange={(checked) => {
                            field.onChange(checked ? "Running" : "Stopped");
                          }}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <DialogFooter>
                  <Button
                    variant="outline"
                    type="button"
                    onClick={() => setIsAddServerDialogOpen(false)}
                  >
                    取消
                  </Button>
                  <Button type="submit">
                    {selectedResource ? "修改" : "添加"}服务器
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog> */}
      </div>
      <>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="text-center">服务器名称</TableHead>
              <TableHead className="text-center">IP地址</TableHead>
              <TableHead className="text-center">端口</TableHead>
              <TableHead className="text-center">最大并发数</TableHead>
              <TableHead className="text-center">当前任务数</TableHead>
              <TableHead className="text-center">状态</TableHead>
              {/* <TableHead className="text-center">操作</TableHead> */}
            </TableRow>
          </TableHeader>
          <TableBody>
            {servers.map((server) => (
              <TableRow key={server.id}>
                <TableCell className="font-medium">{server.name}</TableCell>
                <TableCell>{server.ip}</TableCell>
                <TableCell>{server.port}</TableCell>
                <TableCell>{server.maxProjects}</TableCell>
                <TableCell>{server.curProjects}</TableCell>
                <TableCell>
                  <div className="flex items-center justify-center gap-2">
                    <span className="text-sm text-muted-foreground">
                      {server.status === "Running" ? "运行中" : "已停用"}
                    </span>
                  </div>
                </TableCell>
                {/* <TableCell className="text-right">
                  <div className="flex justify-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSaveServerConfig(server)}
                    >
                      编辑
                    </Button>

                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="outline" size="icon">
                          <Trash className="h-4 w-4 text-destructive" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>确认删除</AlertDialogTitle>
                          <AlertDialogDescription>
                            您确定要删除用户 {server.name} 吗？此操作不可逆。
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>取消</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDeleteServer(server.id)}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                          >
                            删除
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </TableCell> */}
              </TableRow>
            ))}
          </TableBody>
        </Table>
        {totalPages > 1 && (
          <div className="mt-4 flex justify-center">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() =>
                      handlePageChange(Math.max(1, currentPage - 1))
                    }
                    className={
                      currentPage === 1
                        ? "pointer-events-none opacity-50"
                        : "cursor-pointer"
                    }
                  />
                </PaginationItem>

                {getPageNumbers().map((number, index) => (
                  <PaginationItem key={index}>
                    {number === "ellipsis" ? (
                      <PaginationEllipsis />
                    ) : (
                      <PaginationLink
                        isActive={currentPage === number}
                        onClick={() => handlePageChange(number as number)}
                        className="cursor-pointer"
                      >
                        {number}
                      </PaginationLink>
                    )}
                  </PaginationItem>
                ))}

                <PaginationItem>
                  <PaginationNext
                    onClick={() =>
                      handlePageChange(Math.min(totalPages, currentPage + 1))
                    }
                    className={
                      currentPage === totalPages
                        ? "pointer-events-none opacity-50"
                        : "cursor-pointer"
                    }
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </>
    </CustomMainLayout>
  );
};

export default ResourceManagement;
