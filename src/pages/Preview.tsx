import { useEffect } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { RProject, type Project } from "@/request/project";
import { useToast } from "@/components/ui/use-toast";

const Preview = () => {
  const params = useParams();
  const projectId = params.projectId;
  const { toast } = useToast();

  useEffect(() => {
    let script;
    const videoElement = document.getElementById("video"); // 获取 video 元素
    const playBtn = document.getElementById("playBtn"); // 获取播放按钮

    const runProject = async () => {
      try {
        const { data } = await RProject(projectId);
        if (data.publicUrl) {
          fetch_base_url = data.publicUrl;
          fetch_ws_url = import.meta.env.VITE_WS_URL;
          const loadScript = async () => {
            script = document.createElement("script");
            script.src = "/client.js";
            script.async = true; // 设置为异步加载，不影响页面渲染速度
            document.body.appendChild(script);

            return new Promise((resolve) => {
              script.onload = resolve;
            });
          };

          loadScript().then(() => {
            console.log("Script loaded and executed");

            start();

            // 确保视频元素已加载到 DOM 中
            if (videoElement) {
              setupVideoListeners(videoElement, playBtn);
            }
          });
        } else {
          toast({
            title: "项目资源链接不存在",
            description: `请先启动项目`,
            variant: "destructive",
          });
        }
      } catch (e) {
        toast({
          title: "加载项目失败",
          description: `${e.message ?? "请检查网络或联系系统管理员"}`,
          variant: "destructive",
        });
      }
    };

    runProject();

    return () => {
      pc?.close();
      if (script && script.parentNode) {
        script.parentNode.removeChild(script);
        console.log("组件卸载");
      }

      // 清理事件监听器
      if (videoElement) {
        cleanupVideoListeners(videoElement);
      }
    };
  }, []);

  // 设置视频事件监听器
  const setupVideoListeners = (videoElement, playBtn) => {
    // 尝试自动播放
    const attemptAutoPlay = () => {
      videoElement
        .play()
        .then(() => {
          console.log("视频自动播放成功");
          if (playBtn) {
            playBtn.style.display = "none"; // 隐藏按钮
          }
        })
        .catch((err) => {
          console.error("视频自动播放失败:", err);
          if (playBtn) {
            playBtn.style.display = "block"; // 显示按钮
          }
        });
    };

    // 监听视频播放事件
    videoElement.addEventListener("play", () => {
      if (playBtn) {
        playBtn.style.display = "none"; // 隐藏按钮
      }
    });

    // 监听视频错误事件
    videoElement.addEventListener("error", () => {
      if (playBtn) {
        playBtn.style.display = "block"; // 显示按钮
      }
    });

    // 初始尝试自动播放
    attemptAutoPlay();
  };

  // 清理视频事件监听器
  const cleanupVideoListeners = (videoElement) => {
    videoElement.removeEventListener("play", () => {});
    videoElement.removeEventListener("error", () => {});
  };

  return (
    <div>
      <div
        style={{
          display: "flex",
          position: "relative",
          justifyContent: "center",
          width: "100%",
        }}
      >
        <div
          style={{
            display: "flex",
            position: "relative",
            justifyContent: "center",
            width: "600px",
          }}
        >
          {/* 默认隐藏播放按钮 */}
          <button
            id="playBtn"
            onClick={() => {
              const video = document.getElementById("video");
              if (video) {
                video.play().catch((err) => {
                  console.error("手动播放失败:", err);
                });
              }
            }}
            style={{ display: "none" }} // 初始状态下隐藏按钮
          >
            由于浏览器安全策略限制，请点击此按钮允许数字人自动播放
          </button>

          <video
            id="video"
            style={{ width: "540px", height: "820px", display: "block" }}
            autoPlay={true}
            playsInline={true}
            muted={true} // 添加 muted 属性以提高自动播放成功率
          ></video>
          <canvas
            id="canvas"
            style={{ width: "1px", height: "1px", display: "none" }}
          ></canvas>
          <audio id="audio" autoPlay={true}></audio>
          <input type="hidden" id="sessionid" value="0"></input>

          <div id="chat-container">
            <div id="recordButtonContainer">
              <button id="recordButton" className="button">
                <img src="/vector.png" alt="按钮图标"></img>
              </button>
              <div id="cancelContainer">
                <span className="shine-text">滑动取消</span>
                <img
                  src="/chevron_down.png"
                  alt="向下箭头"
                  style={{
                    width: "16px",
                    height: "16px",
                    objectFit: "contain",
                  }}
                ></img>
              </div>
            </div>
            <div id="chat-messages" style={{ margin: "0px 60px" }}></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Preview;
