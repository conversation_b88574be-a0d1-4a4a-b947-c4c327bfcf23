import PageTransition from "@/components/ui/PageTransition";
import MainLayout from "@/components/layout/MainLayout";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Code, Copy, Check, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

const DevelopmentGuide = () => {
  const [copiedTab, setCopiedTab] = useState<string | null>(null);

  const handleCopyCode = (codeSnippet: string, tabId: string) => {
    navigator.clipboard.writeText(codeSnippet);
    setCopiedTab(tabId);
    setTimeout(() => setCopiedTab(null), 2000);
  };

  return (
    <MainLayout>
      <PageTransition>
        <div className="container max-w-7xl px-4 py-8">
          <div className="flex flex-col">
            <Card className="mb-8">
              <CardHeader>
                <CardTitle>集成方式</CardTitle>
                <CardDescription>
                  我们提供了两种集成方式，以满足不同的应用场景和技术需求
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="page-embed" className="w-full">
                  <TabsList className="w-full grid grid-cols-2 mb-6">
                    <TabsTrigger value="page-embed">页面嵌入</TabsTrigger>
                    <TabsTrigger value="sdk">SDK对接</TabsTrigger>
                  </TabsList>

                  <TabsContent value="page-embed" className="space-y-4">
                    <div className="text-sm text-muted-foreground mb-4">
                      页面嵌入是最简单的集成方式，只需要几步操作即可将数字人嵌入到您的网站中。
                    </div>

                    <div className="space-y-4">
                      <div className="flex">
                        <div className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center font-medium mr-4">
                          1
                        </div>
                        <div className="flex-1">
                          <h3 className="text-base font-medium mb-2">
                            进入我的项目页面
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            在控制台导航栏中点击"我的项目"，进入项目列表页面。
                          </p>
                        </div>
                      </div>

                      <div className="flex">
                        <div className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center font-medium mr-4">
                          2
                        </div>
                        <div className="flex-1">
                          <h3 className="text-base font-medium mb-2">
                            点击启动项目
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            在项目卡片上找到"启动"按钮并点击，系统会启动您的数字人项目。
                          </p>
                        </div>
                      </div>

                      <div className="flex">
                        <div className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center font-medium mr-4">
                          3
                        </div>
                        <div className="flex-1">
                          <h3 className="text-base font-medium mb-2">
                            获取嵌入链接
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            在弹出的新页面中，复制浏览器地址栏中的URL，即可获得嵌入链接。
                          </p>
                        </div>
                      </div>

                      <div className="mt-6 bg-muted p-4 rounded-md">
                        <h4 className="text-sm font-medium mb-2">
                          HTML嵌入示例
                        </h4>
                        <div className="relative">
                          <pre className="text-xs bg-black text-white p-4 rounded-md overflow-x-auto">
                            {`<!-- 通过iframe嵌入数字人页面 -->
<iframe 
  src="https://your-project-url" 
  width="100%" 
  height="600" 
  frameborder="0" 
  allow="microphone; camera"
></iframe>`}
                          </pre>
                          <Button
                            size="icon"
                            variant="ghost"
                            className="absolute top-2 right-2"
                            onClick={() =>
                              handleCopyCode(
                                `<!-- 通过iframe嵌入数字人页面 -->
<iframe 
  src="https://your-project-url" 
  width="100%" 
  height="600" 
  frameborder="0" 
  allow="microphone; camera"
></iframe>`,
                                "iframe"
                              )
                            }
                          >
                            {copiedTab === "iframe" ? (
                              <Check className="h-4 w-4" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="sdk" className="space-y-4">
                    <div className="text-sm text-muted-foreground mb-4">
                      SDK对接为开发者提供更灵活的集成方式，您可以通过API完全控制数字人的行为和交互流程。
                    </div>

                    <Accordion type="single" collapsible className="w-full">
                      <AccordionItem value="item-1">
                        <AccordionTrigger className="text-base font-medium">
                          <div className="flex items-center">
                            <div className="w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-xs font-medium mr-3">
                              1
                            </div>
                            认证接口
                          </div>
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-4 pl-9">
                            <p className="text-sm text-muted-foreground">
                              首先，您需要通过认证接口获取访问令牌，用于后续API调用的身份验证。
                            </p>
                            <div className="relative">
                              <pre className="bg-muted p-4 rounded-md overflow-x-auto text-sm">
                                {`// 使用 fetch API 获取访问令牌
fetch('https://api.example.com/auth/token', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    apiKey: 'YOUR_API_KEY',
    secret: 'YOUR_API_SECRET'
  })
})
.then(response => response.json())
.then(data => {
  // 存储访问令牌
  const accessToken = data.access_token;
  console.log('Access Token:', accessToken);
})
.catch(error => console.error('Error:', error));`}
                              </pre>
                              <Button
                                size="icon"
                                variant="ghost"
                                className="absolute top-2 right-2"
                                onClick={() =>
                                  handleCopyCode(
                                    `// 使用 fetch API 获取访问令牌
fetch('https://api.example.com/auth/token', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    apiKey: 'YOUR_API_KEY',
    secret: 'YOUR_API_SECRET'
  })
})
.then(response => response.json())
.then(data => {
  // 存储访问令牌
  const accessToken = data.access_token;
  console.log('Access Token:', accessToken);
})
.catch(error => console.error('Error:', error));`,
                                    "auth"
                                  )
                                }
                              >
                                {copiedTab === "auth" ? (
                                  <Check className="h-4 w-4" />
                                ) : (
                                  <Copy className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>

                      <AccordionItem value="item-2">
                        <AccordionTrigger className="text-base font-medium">
                          <div className="flex items-center">
                            <div className="w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-xs font-medium mr-3">
                              2
                            </div>
                            建立连接
                          </div>
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-4 pl-9">
                            <p className="text-sm text-muted-foreground">
                              获取令牌后，您需要与数字人服务建立WebSocket连接，以实现实时交互。
                            </p>
                            <div className="relative">
                              <pre className="bg-muted p-4 rounded-md overflow-x-auto text-sm">
                                {`// 建立WebSocket连接
const connectToDigitalHuman = (token, projectId) => {
  const socket = new WebSocket(\`wss://api.example.com/ws?token=\${token}&projectId=\${projectId}\`);
  
  socket.onopen = () => {
    console.log('WebSocket连接已建立');
    // 存储socket实例供后续使用
    window.digitalHumanSocket = socket;
  };
  
  socket.onmessage = (event) => {
    const message = JSON.parse(event.data);
    console.log('收到消息:', message);
    // 处理接收到的数字人响应
    handleDigitalHumanResponse(message);
  };
  
  socket.onerror = (error) => {
    console.error('WebSocket错误:', error);
  };
  
  socket.onclose = () => {
    console.log('WebSocket连接已关闭');
  };
  
  return socket;
};

// 调用连接函数
const socket = connectToDigitalHuman(accessToken, 'YOUR_PROJECT_ID');`}
                              </pre>
                              <Button
                                size="icon"
                                variant="ghost"
                                className="absolute top-2 right-2"
                                onClick={() =>
                                  handleCopyCode(
                                    `// 建立WebSocket连接
const connectToDigitalHuman = (token, projectId) => {
  const socket = new WebSocket(\`wss://api.example.com/ws?token=\${token}&projectId=\${projectId}\`);
  
  socket.onopen = () => {
    console.log('WebSocket连接已建立');
    // 存储socket实例供后续使用
    window.digitalHumanSocket = socket;
  };
  
  socket.onmessage = (event) => {
    const message = JSON.parse(event.data);
    console.log('收到消息:', message);
    // 处理接收到的数字人响应
    handleDigitalHumanResponse(message);
  };
  
  socket.onerror = (error) => {
    console.error('WebSocket错误:', error);
  };
  
  socket.onclose = () => {
    console.log('WebSocket连接已关闭');
  };
  
  return socket;
};

// 调用连接函数
const socket = connectToDigitalHuman(accessToken, 'YOUR_PROJECT_ID');`,
                                    "connect"
                                  )
                                }
                              >
                                {copiedTab === "connect" ? (
                                  <Check className="h-4 w-4" />
                                ) : (
                                  <Copy className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>

                      <AccordionItem value="item-3">
                        <AccordionTrigger className="text-base font-medium">
                          <div className="flex items-center">
                            <div className="w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-xs font-medium mr-3">
                              3
                            </div>
                            发送消息
                          </div>
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-4 pl-9">
                            <p className="text-sm text-muted-foreground">
                              通过建立的WebSocket连接，您可以向数字人发送消息，获取实时响应。
                            </p>
                            <div className="relative">
                              <pre className="bg-muted p-4 rounded-md overflow-x-auto text-sm">
                                {`// 发送消息给数字人
const sendMessageToDigitalHuman = (message, sessionId = null) => {
  if (!window.digitalHumanSocket || window.digitalHumanSocket.readyState !== WebSocket.OPEN) {
    console.error('WebSocket未连接');
    return;
  }
  
  const payload = {
    type: 'message',
    content: message,
    sessionId: sessionId, // 首次对话不需要sessionId
  };
  
  window.digitalHumanSocket.send(JSON.stringify(payload));
  console.log('消息已发送:', payload);
};

// 处理数字人响应
const handleDigitalHumanResponse = (response) => {
  if (response.type === 'message') {
    // 显示文本回复
    console.log('数字人回复:', response.content);
    
    // 如果是首次回复，保存会话ID
    if (response.sessionId && !window.currentSessionId) {
      window.currentSessionId = response.sessionId;
    }
    
    // 更新UI以显示回复
    // updateChatUI(response.content);
  } else if (response.type === 'audio') {
    // 处理音频响应
    // playAudio(response.audioUrl);
  } else if (response.type === 'video') {
    // 处理视频响应
    // updateVideo(response.videoData);
  }
};

// 使用示例
sendMessageToDigitalHuman("你好，请介绍一下你自己");`}
                              </pre>
                              <Button
                                size="icon"
                                variant="ghost"
                                className="absolute top-2 right-2"
                                onClick={() =>
                                  handleCopyCode(
                                    `// 发送消息给数字人
const sendMessageToDigitalHuman = (message, sessionId = null) => {
  if (!window.digitalHumanSocket || window.digitalHumanSocket.readyState !== WebSocket.OPEN) {
    console.error('WebSocket未连接');
    return;
  }
  
  const payload = {
    type: 'message',
    content: message,
    sessionId: sessionId, // 首次对话不需要sessionId
  };
  
  window.digitalHumanSocket.send(JSON.stringify(payload));
  console.log('消息已发送:', payload);
};

// 处理数字人响应
const handleDigitalHumanResponse = (response) => {
  if (response.type === 'message') {
    // 显示文本回复
    console.log('数字人回复:', response.content);
    
    // 如果是首次回复，保存会话ID
    if (response.sessionId && !window.currentSessionId) {
      window.currentSessionId = response.sessionId;
    }
    
    // 更新UI以显示回复
    // updateChatUI(response.content);
  } else if (response.type === 'audio') {
    // 处理音频响应
    // playAudio(response.audioUrl);
  } else if (response.type === 'video') {
    // 处理视频响应
    // updateVideo(response.videoData);
  }
};

// 使用示例
sendMessageToDigitalHuman("你好，请介绍一下你自己");`,
                                    "send"
                                  )
                                }
                              >
                                {copiedTab === "send" ? (
                                  <Check className="h-4 w-4" />
                                ) : (
                                  <Copy className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>

                      <AccordionItem value="item-4">
                        <AccordionTrigger className="text-base font-medium">
                          <div className="flex items-center">
                            <div className="w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-xs font-medium mr-3">
                              4
                            </div>
                            断开连接
                          </div>
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-4 pl-9">
                            <p className="text-sm text-muted-foreground">
                              在用户会话结束时，应当优雅地关闭WebSocket连接并释放资源。
                            </p>
                            <div className="relative">
                              <pre className="bg-muted p-4 rounded-md overflow-x-auto text-sm">
                                {`// 断开数字人连接
const disconnectFromDigitalHuman = () => {
  if (window.digitalHumanSocket && window.digitalHumanSocket.readyState === WebSocket.OPEN) {
    // 发送结束会话信号
    const payload = {
      type: 'end',
      sessionId: window.currentSessionId
    };
    
    window.digitalHumanSocket.send(JSON.stringify(payload));
    
    // 关闭WebSocket连接
    window.digitalHumanSocket.close();
    console.log('已断开数字人连接');
    
    // 清理会话数据
    window.currentSessionId = null;
    window.digitalHumanSocket = null;
  }
};

// 页面卸载前断开连接
window.addEventListener('beforeunload', () => {
  disconnectFromDigitalHuman();
});

// 手动调用断开连接
// disconnectFromDigitalHuman();`}
                              </pre>
                              <Button
                                size="icon"
                                variant="ghost"
                                className="absolute top-2 right-2"
                                onClick={() =>
                                  handleCopyCode(
                                    `// 断开数字人连接
const disconnectFromDigitalHuman = () => {
  if (window.digitalHumanSocket && window.digitalHumanSocket.readyState === WebSocket.OPEN) {
    // 发送结束会话信号
    const payload = {
      type: 'end',
      sessionId: window.currentSessionId
    };
    
    window.digitalHumanSocket.send(JSON.stringify(payload));
    
    // 关闭WebSocket连接
    window.digitalHumanSocket.close();
    console.log('已断开数字人连接');
    
    // 清理会话数据
    window.currentSessionId = null;
    window.digitalHumanSocket = null;
  }
};

// 页面卸载前断开连接
window.addEventListener('beforeunload', () => {
  disconnectFromDigitalHuman();
});

// 手动调用断开连接
// disconnectFromDigitalHuman();`,
                                    "disconnect"
                                  )
                                }
                              >
                                {copiedTab === "disconnect" ? (
                                  <Check className="h-4 w-4" />
                                ) : (
                                  <Copy className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        </div>
      </PageTransition>
    </MainLayout>
  );
};

export default DevelopmentGuide;
