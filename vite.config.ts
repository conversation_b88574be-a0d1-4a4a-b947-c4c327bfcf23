import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    proxy: {
      '/chat': {
        target: 'http://***********:37861', // 目标服务器地址
        changeOrigin: true, // 允许跨域
      },
      '/v1': {
        target: 'http://localhost:10070', // 目标服务器地址
        changeOrigin: true, // 允许跨域
      },
      '/static': {
        target: 'http://localhost:10070', // 目标服务器地址
        changeOrigin: true, // 允许跨域
      }
    },

  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
