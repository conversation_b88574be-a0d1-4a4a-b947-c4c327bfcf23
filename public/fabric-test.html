<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fabric.js Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/6.6.4/fabric.min.js"></script>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f0f0f0;
        }
        #canvas-container {
            border: 1px solid #ccc;
            background-color: white;
        }
    </style>
</head>
<body>
    <div id="canvas-container">
        <canvas id="canvas" width="800" height="450"></canvas>
    </div>

    <script>
        // Initialize Fabric.js canvas
        const canvas = new fabric.Canvas('canvas', {
            backgroundColor: '#ffffff',
            preserveObjectStacking: true,
            selection: true
        });

        // Add video element (placeholder for digital human)
        const videoRect = new fabric.Rect({
            left: canvas.width / 2 - 150,
            top: canvas.height / 2 - 200,
            width: 300,
            height: 400,
            fill: '#e0e0e0',
            stroke: '#999999',
            strokeWidth: 1,
            rx: 10,
            ry: 10,
            name: 'videoElement',
            selectable: true,
        });

        const videoText = new fabric.Text('数字人视频区域', {
            left: canvas.width / 2 - 60,
            top: canvas.height / 2 - 10,
            fontSize: 16,
            fill: '#666666',
            name: 'videoText',
            selectable: false,
        });

        // Add record button
        const recordCircle = new fabric.Circle({
            left: canvas.width / 2 - 30,
            top: canvas.height - 100,
            radius: 30,
            fill: '#ff4d4f',
            stroke: '#ff7875',
            strokeWidth: 2,
            name: 'recordButton',
            selectable: true,
        });

        const recordText = new fabric.Text('录音', {
            left: canvas.width / 2 - 15,
            top: canvas.height - 90,
            fontSize: 14,
            fill: '#ffffff',
            name: 'recordText',
            selectable: false,
        });

        // Add text input button
        const textInputRect = new fabric.Rect({
            left: canvas.width - 100,
            top: canvas.height - 100,
            width: 80,
            height: 40,
            fill: '#1677ff',
            rx: 5,
            ry: 5,
            name: 'textInputButton',
            selectable: true,
        });

        const textInputText = new fabric.Text('文本输入', {
            left: canvas.width - 90,
            top: canvas.height - 90,
            fontSize: 14,
            fill: '#ffffff',
            name: 'textInputText',
            selectable: false,
        });

        // Add subtitle area
        const subtitleRect = new fabric.Rect({
            left: canvas.width / 2 - 200,
            top: canvas.height - 50,
            width: 400,
            height: 40,
            fill: 'rgba(0, 0, 0, 0.5)',
            rx: 5,
            ry: 5,
            name: 'subtitleArea',
            selectable: true,
        });

        const subtitleText = new fabric.Text('字幕区域 (对话内容)', {
            left: canvas.width / 2 - 80,
            top: canvas.height - 40,
            fontSize: 16,
            fill: '#ffffff',
            name: 'subtitleText',
            selectable: false,
        });

        // Group related elements
        const videoGroup = new fabric.Group([videoRect, videoText], {
            name: 'videoGroup',
            selectable: true,
        });

        const recordGroup = new fabric.Group([recordCircle, recordText], {
            name: 'recordGroup',
            selectable: true,
        });

        const textInputGroup = new fabric.Group([textInputRect, textInputText], {
            name: 'textInputGroup',
            selectable: true,
        });

        const subtitleGroup = new fabric.Group([subtitleRect, subtitleText], {
            name: 'subtitleGroup',
            selectable: true,
        });

        // Add all elements to canvas
        canvas.add(videoGroup, recordGroup, textInputGroup, subtitleGroup);
        canvas.renderAll();
    </script>
</body>
</html>
