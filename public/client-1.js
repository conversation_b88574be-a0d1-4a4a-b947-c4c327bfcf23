var pc = null;
var checkedChromKey = false;
var wsconnecter = null;
let timer = null;
var sampleBuf = new Int16Array();
var offline_text = "";
var rec_text = "";
var isSpeaking = false;
var useTurn = false;
let isCanceled = false;

const welcomeText = "您好，我是智能医保助手，请问有什么可以帮助您的吗？";
const video = document.getElementById("video");
const canvas = document.getElementById("canvas");
const ctx = canvas.getContext("2d", { willReadFrequently: true });

var rec = Recorder({
  type: "pcm",
  bitRate: 16,
  sampleRate: 16000,
  onProcess: recProcess,
});

function processVideoFrame() {
  /*canvas.width = video.videoWidth / 2.3;
    canvas.height = video.videoHeight / 2.3;
    if (canvas.width > 0 && isSpeaking == false) {
        document.getElementById('video').pause();
    }*/

  ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const data = imageData.data;

  if (checkedChromKey) {
    for (let i = 0; i < data.length; i += 4) {
      const red = data[i];
      const green = data[i + 1];
      const blue = data[i + 2];

      //if (red < 75 && green < 150 && blue > 90) {   // process blue
      if (red < 80 && green > 90 && blue < 80) {
        // process green
        data[i + 3] = 0; // 设置 alpha 值为 0，使其透明
      }
    }
  }
  ctx.putImageData(imageData, 0, 0);
  requestAnimationFrame(processVideoFrame);
}
function generateRandomState() {
  return Math.random().toString(36).substring(2, 15); // 生成随机字符串
}

function negotiate() {
  pc.addTransceiver("video", { direction: "recvonly" });
  pc.addTransceiver("audio", { direction: "recvonly" });
  return pc
    .createOffer()
    .then((offer) => {
      return pc.setLocalDescription(offer);
    })
    .then(() => {
      // wait for ICE gathering to complete
      return new Promise((resolve) => {
        if (pc.iceGatheringState === "complete") {
          resolve();
        } else {
          const checkState = () => {
            if (pc.iceGatheringState === "complete") {
              pc.removeEventListener("icegatheringstatechange", checkState);
              resolve();
            }
          };
          pc.addEventListener("icegatheringstatechange", checkState);
        }
      });
    })
    .then(() => {
      var offer = pc.localDescription;
      return fetch(`${fetch_base_url}/offer`, {
        // return fetch('/offer', {
        body: JSON.stringify({
          sdp: offer.sdp,
          type: offer.type,
        }),
        headers: {
          "Content-Type": "application/json",
        },
        method: "POST",
      });
    })
    .then((response) => {
      if (response?.status == 500) {
        return Promise.reject({ status: 500 });
      } else {
        return response.json();
      }
    })
    .then((answer) => {
      document.getElementById("sessionid").value = answer.sessionid;
      return pc.setRemoteDescription(answer);
    })
    .catch((e) => {
      console.error("======fetch error", e);
      if (e?.status == 500) {
        alert("服务器最大并发数已达上限，请联系管理员处理");
      } else {
        const redirectUri = encodeURIComponent(window.location.href);
        const state = generateRandomState();
        window.location.href = `${fetch_base_url}/Auth.html?redirect_uri=${redirectUri}&state=${state}`;
      }
    });
}

function start() {
  var config = {
    sdpSemantics: "unified-plan",
  };

  if (useTurn) {
    config.iceServers = [
      {
        urls: ["stun:stun.l.google.com:19302"],
      },
      {
        urls: ["turn:*************:3478?transport=udp"],
        username: "tslapp",
        credential: "3A2a9ePPTnA",
      },
    ];
  }

  pc = new RTCPeerConnection(config);

  // connect audio / video
  pc.addEventListener("track", (evt) => {
    if (evt.track.kind == "video") {
      const videoElement = document.getElementById("video");
      console.log("start video");
      videoElement.srcObject = evt.streams[0];

      var promise = videoElement.play();

      if (promise !== undefined) {
        promise
          .then((_) => {
            console.log("Autoplay started!");
            // Autoplay started!
          })
          .catch((error) => {
            console.log("Autoplay was prevented.");
            // playButton.style.display = "block";
            // Autoplay was prevented.
            // Show a "Play" button so that user can start playback.
          });
      }
    } else {
      document.getElementById("audio").srcObject = evt.streams[0];

      //----------------------check Silence----------------------------------
      const audioContext = new AudioContext();
      const analyser = audioContext.createAnalyser();
      const source = audioContext.createMediaStreamSource(evt.streams[0]);
      source.connect(analyser);

      const dataArray = new Uint8Array(analyser.fftSize);
      let silenceStartTime = null;
      const silenceThreshold = 1000; // 静音持续时间（毫秒）

      const checkSilence = () => {
        analyser.getByteTimeDomainData(dataArray);
        // 计算平均振幅（0-255），振幅接近 128 表示接近静音
        const average =
          dataArray.reduce((sum, value) => sum + Math.abs(value - 128), 0) /
          dataArray.length;
        if (average < 1) {
          // 检测到静音
          if (!silenceStartTime) {
            silenceStartTime = Date.now(); // 记录静音开始时间
          } else if (
            Date.now() - silenceStartTime >= silenceThreshold &&
            !document.getElementById("video").paused
          ) {
            //console.log('Audio has been silent for 3 seconds. Playback considered complete.');
            isSpeaking = false;
            //document.getElementById('video').pause();
          }
        } else {
          // 非静音，重置静音开始时间
          silenceStartTime = null;
          isSpeaking = true;
          //document.getElementById('video').play();
        }
        requestAnimationFrame(checkSilence); // 持续检查
      };
      checkSilence();
      //---------------------------------------------------------------
    }
  });

  //document.getElementById('start').style.display = 'none';
  negotiate().then(() => {
    //document.getElementById('stop').style.display = 'inline-block';
    document.getElementById("canvas").style.display = "inline-block";

    setTimeout(() => {
      sendToHuman(welcomeText, false);
      setTimeout(() => {
        updateMessage(welcomeText, true, false);
      }, 1000);
    }, 2000);
    //video.addEventListener('play', processVideoFrame);

    // startRecording();
  });
}

function stop() {
  setTimeout(() => {
    pc.close();
  }, 500);

  //document.getElementById('stop').style.display = 'none';
  document.getElementById("canvas").style.display = "none";
  //document.getElementById('start').style.display = 'inline-block';
}

window.onunload = function (event) {
  setTimeout(() => {
    pc.close();
  }, 500);
};

window.onbeforeunload = function (e) {
  setTimeout(() => {
    pc.close();
  }, 500);
  e = e || window.event;
  // 兼容IE8和Firefox 4之前的版本
  if (e) {
    e.returnValue = "关闭提示";
  }
  // Chrome, Safari, Firefox 4+, Opera 12+ , IE 9+
  return "关闭提示";
};

function splitTextBySentences(text) {
  const sentences = text
    .split(/[。！？\n]/)
    .filter((sentence) => sentence.trim() !== "");
  return sentences.map((sentence) => sentence.trim());
}

function isStopCommand(input) {
  const stopWords = [
    "停止",
    "结束",
    "暂停",
    "退出",
    "停下",
    "停掉",
    "停止一下",
    "停",
    "关掉",
    "关闭",
    "终止",
    "别说了",
  ];

  // 将输入转换为小写，并去掉前后空格
  input = input.trim();

  // 判断输入的文本是否包含停止相关的词汇
  return stopWords.some((word) => input.includes(word));
}

async function postToLLM(url, data) {
  const headers = {
    "Content-Type": "application/json",
    timeout: 60000,
  };

  const response = await fetch(url, {
    method: "POST",
    headers: headers,
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  let done = false;
  let resultBuffer = ""; // 缓存接收到的数据
  let sentenceBuffer = ""; // 用于临时缓存当前句子
  let isLast = false;

  while (!done) {
    const { value, done: readerDone } = await reader.read();
    done = readerDone;
    resultBuffer += decoder.decode(value, { stream: true }); // 将当前流解码并拼接

    while (true) {
      const jsonStartIndex = resultBuffer.indexOf("data: {");
      const jsonEndIndex = resultBuffer.indexOf("}", jsonStartIndex);

      if (jsonStartIndex !== -1 && jsonEndIndex !== -1) {
        const jsonString = resultBuffer.slice(
          jsonStartIndex + 6,
          jsonEndIndex + 1
        );
        try {
          const jsonData = JSON.parse(jsonString);
          const text = jsonData.answer; // 提取 'text' 字段的内容
          if (text) {
            sentenceBuffer += text; // 拼接文本到当前句子缓存
            updateMessage(text, true, isLast);
            isLast = true;
            const sentenceEndIndex = sentenceBuffer.search(/[。！？\n]/);
            if (sentenceEndIndex !== -1) {
              const completeSentence = sentenceBuffer
                .slice(0, sentenceEndIndex + 1)
                .trim();
              if (completeSentence) {
                sendToHuman(completeSentence, false);
              }
              sentenceBuffer = sentenceBuffer
                .slice(sentenceEndIndex + 1)
                .trim();
            }
          }
        } catch (error) {
          console.error("JSON 解析错误:", error);
        }
        resultBuffer = resultBuffer.slice(jsonEndIndex + 1);
      } else {
        break;
      }
    }
  }
  reader.releaseLock();
}

function sendToHuman(message, interrupt) {
  console.log("sendToHuman:", message, interrupt);
  fetch(`${fetch_base_url}/human`, {
    // fetch("/human", {
    body: JSON.stringify({
      text: message,
      type: "echo",
      interrupt: interrupt,
      sessionid: parseInt(document.getElementById("sessionid").value),
    }),
    headers: {
      "Content-Type": "application/json",
    },
    method: "POST",
  });
  //document.getElementById('video').play();
}

function getConnState(connState) {
  if (connState === 0) {
  } else if (connState === 1) {
    //stop();
  } else if (connState === 2) {
    stop();
  }
}

function getJsonMessage(jsonMsg) {
  let rectxt = JSON.parse(jsonMsg.data)["text"];
  let asrmodel = JSON.parse(jsonMsg.data)["mode"];
  let is_final = JSON.parse(jsonMsg.data)["is_final"];

  if (asrmodel == "2pass-offline" || asrmodel == "offline") {
    offline_text = offline_text + rectxt;
    rec_text = offline_text;
  } else {
    rec_text = rec_text;
  }

  rec_text = rec_text.replace(/<\|.*?\|>/g, "");
  console.log("is final:", is_final);
  if (is_final == true) {
    wsconnecter.wsStop();
    // asr_finish = true;
    sendToLLM();
  }
  console.log("asr rec_text:", rec_text);
  //updateMessage(rec_text, false, false);
}

async function sendToLLM() {
  if (!isCanceled) {
    if (isStopCommand(rec_text)) {
      sendToHuman("请问还有什么可以帮助您的？", true);
      updateMessage("请问还有什么可以帮助您的？", true, false);
    } else if (rec_text && !isSpeaking && rec_text.length > 4) {
      console.log("chatLLM:", rec_text.trim());
      updateMessage(rec_text, false, false);
      const data = {
        query: rec_text.trim() + "要求回答尽量精简。",
        knowledge_base_name: "yibao",
        top_k: 10,
        history_len: -1,
        history: [],
        stream: true,
        model_name: "Bit-LLM-Small",
        temperature: 0.5,
        max_tokens: 0,
        prompt_name: "default",
      };
      await postToLLM(
        "/chat/knowledge_base_chat",
        // `${fetch_base_url}/api/chat/knowledge_base_chat`,
        data
      );
    }
  }
  console.log("清理rec_text");
  rec_text = "";
  offline_text = "";
}

function recProcess(
  buffer,
  powerLevel,
  bufferDuration,
  bufferSampleRate,
  newBufferIdx,
  asyncEnd
) {
  if (wsconnecter) {
    var data_48k = buffer[buffer.length - 1];

    var array_48k = new Array(data_48k);
    var data_16k = Recorder.SampleData(array_48k, bufferSampleRate, 16000).data;

    sampleBuf = Int16Array.from([...sampleBuf, ...data_16k]);
    var chunk_size = 960; // for asr chunk_size [5, 10, 5]
    while (sampleBuf.length >= chunk_size) {
      let sendBuf = sampleBuf.slice(0, chunk_size);
      sampleBuf = sampleBuf.slice(chunk_size, sampleBuf.length);
      console.log("asr sendBuf:");
      wsconnecter.wsSend(sendBuf);
    }
  }
}

function startRecording() {
  console.log("录音开始");

  if (wsconnecter) {
    wsconnecter?.wsStop();
    wsconnecter = null;
  }
  wsconnecter = new WebSocketConnectMethod({
    msgHandle: getJsonMessage,
    stateHandle: getConnState,
  });
  const ret = wsconnecter.wsStart();
  rec.open(function () {
    if (wsconnecter) {
      rec.start();
    }
  });
}
function stopRecording() {
  console.log("录音结束");
  // if (wsconnecter) {
  //   wsconnecter?.wsStop();
  //   wsconnecter = null;
  // }
  // rec.stop(undefined,undefined,true);
  try {
    if (wsconnecter) {
      rec.stop(undefined, undefined, true);
      var chunk_size = new Array(5, 10, 5);
      var request = {
        chunk_size: chunk_size,
        wav_name: "h5",
        is_speaking: false,
        chunk_interval: 10,
        mode: "offline",
      };
      // console.log("===stop request", request);
      if (sampleBuf.length > 0) {
        wsconnecter.wsSend(sampleBuf);
        // console.log("sampleBuf.length" + sampleBuf.length);
        sampleBuf = new Int16Array();
      }
      wsconnecter.wsSend(JSON.stringify(request));
      // setTimeout(() => {
      //   rec.stop(undefined, undefined, true);
      // }, 100);
      //
    }
  } catch (error) {
    console.log("err");
    console.log(error);
  }
}
function updateMessage(message, isAI = true, last = false) {
  const $chatMessages = $("#chat-messages");

  const messageDiv = $("<div>")
    .addClass(isAI ? "ai-message" : "user-message")
    .css({
      padding: "10px 15px",
      "border-radius": "10px",
      "max-width": "80%",
      "align-self": isAI ? "flex-start" : "flex-end",
      //   'backdrop-filter': 'blur(5px)'
    })
    .text(message);

  if (last) {
    const $lastMessage = $chatMessages.children().last();
    if (
      $lastMessage.length &&
      ((isAI && $lastMessage.hasClass("ai-message")) ||
        (!isAI && $lastMessage.hasClass("user-message")))
    ) {
      let lastMessage = $lastMessage.text();
      $lastMessage.text(lastMessage + message);
    } else {
      $chatMessages.append(messageDiv);
    }
  } else {
    $chatMessages.append(messageDiv);
  }
  $chatMessages.scrollTop($chatMessages[0].scrollHeight);
}

const cancelContainer = document.getElementById("cancelContainer");
const button = document.getElementById("recordButton");
let isDragging = false;
let startY, initialY, currentY, targetY;

// 鼠标点击
button.addEventListener("mousedown", (e) => {
  isCanceled = false;
  console.log("鼠标点击");
  e.preventDefault();

  isDragging = true;

  initialY = button.offsetTop;
  startY = e.clientY;
  currentY = initialY;
  targetY = initialY;

  button.classList.add("clicked");
  button.style.transition = "none";
  cancelContainer.style.opacity = "0.9";
  startRecording();
});

// 鼠标拖动
document.addEventListener("mousemove", (e) => {
  if (isDragging) {
    e.preventDefault();
    const dy = e.clientY - startY;

    if (dy > 0 && dy < 100) {
      targetY = initialY + dy;
      currentY += (targetY - currentY) * 0.25; // 调整阻尼系数
      button.style.top = `${currentY}px`;
    }
  }
});

// 鼠标释放
document.addEventListener("mouseup", (e) => {
  // console.log('鼠标释放');
  if (isDragging) {
    isDragging = false;

    button.style.transition =
      "transform 0.3s ease, opacity 0.3s ease, top 0.5s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.2s ease";

    const dy = e.clientY - startY;

    button.style.top = `${initialY}px`;
    button.classList.remove("clicked");

    cancelContainer.style.opacity = "0";

    if (dy > 50) {
      isCanceled = true;
      console.log("取消发送");
    } else {
      console.log("确定发送");
    }
    stopRecording();
  }
});

// 触摸点击
button.addEventListener("touchstart", (e) => {
  isCanceled = false;
  console.log("鼠标点击");
  e.preventDefault();

  isDragging = true;

  initialY = button.offsetTop;
  startY = e.touches[0].clientY;
  currentY = initialY;
  targetY = initialY;

  button.classList.add("clicked");
  button.style.transition = "none";
  cancelContainer.style.opacity = "0.9";

  startRecording();
});

// 触摸拖动
document.addEventListener("touchmove", (e) => {
  if (isDragging) {
    e.preventDefault();
    const dy = e.touches[0].clientY - startY;

    if (dy > 0 && dy < 100) {
      targetY = initialY + dy;
      currentY += (targetY - currentY) * 0.25;
      button.style.top = `${currentY}px`;
    }
  }
});

// 触摸释放
document.addEventListener("touchend", (e) => {
  if (isDragging) {
    isDragging = false;

    button.style.transition =
      "transform 0.3s ease, opacity 0.3s ease, top 0.5s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.2s ease";

    const dy = e.changedTouches[0].clientY - startY;

    button.style.top = `${initialY}px`;
    button.classList.remove("clicked");

    cancelContainer.style.opacity = "0";

    if (dy > 50) {
      isCanceled = true;
      console.log("取消发送");
    } else {
      console.log("确定发送");
    }

    stopRecording();
  }
});

// const playButton = document.createElement("button");

// playButton.innerText =
//   "由于浏览器安全策略限制，请点击此按钮允许数字人自动播放。";
// playButton.style.position = "fixed";
// playButton.style.top = "50%";
// playButton.style.left = "50%";
// playButton.style.transform = "translate(-50%, -50%)";
// playButton.style.padding = "10px 20px";
// playButton.style.fontSize = "16px";
// playButton.style.backgroundColor = "#4C82E6";
// playButton.style.color = "white";
// playButton.style.border = "none";
// playButton.style.borderRadius = "5px";
// playButton.style.cursor = "pointer";
// playButton.style.display = "none"; // 初始隐藏

// document.body.appendChild(playButton);

const playButton = document.getElementById("playBtn");

playButton.addEventListener("click", () => {
  const videoElement = document.getElementById("video");
  videoElement
    .play()
    .then(() => {
      playButton.style.display = "none";
    })
    .catch((error) => {
      console.log("Playback failed:", error);
    });
});
