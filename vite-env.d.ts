/// <reference types="vite/client" />
import 'vue-router';

interface ImportMetaEnv {
    readonly VITE_API_URL: string;
    readonly VITE_WSS_URL: string;
    readonly VITE_CDN_URL: string;
    readonly VITE_PLATFORM: string;
}

interface ImportMeta {
    readonly env: ImportMetaEnv;
}



export interface Response<T = any> {
    config: any;
    status: number;

    data: T;
    statusText: string
}

export interface URLType {
    id: string;
    pid?: string;
}
